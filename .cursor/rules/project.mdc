---
description: 
globs: 
alwaysApply: true
---
There are project description files in the doc/design folder, so I hope you will read the files in this folder before solving the problem, and if the problem you are dealing with is recorded in this folder before, please modify the file content in time. At the same time, I am a beginner of NextJS, so I hope you can tell me some relevant knowledge points every time you solve a problem. Including but not limited to: adding appropriate comments in the code, outputting the design documents of the complete functional module to the doc/design folder...