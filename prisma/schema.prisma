generator client {
  provider = "prisma-client-js"
}

// 数据库配置
datasource db {
  provider  = "postgresql"
  url       = env("DATABASE_URL")
  directUrl = env("DIRECT_URL")
}

// 用户模型
model User {
  id            String     @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  email         String     @unique @db.VarChar(255)
  emailVerified DateTime?  @db.Timestamptz(6)
  username      String?    @unique @db.VarChar(100)
  phone         String?    @unique @db.VarChar(20)
  password      String?    @db.VarChar(255)
  name          String?    @db.VarChar(100)
  image         String?    @db.VarChar(500) // NextAuth需要的字段
  avatar        String?    @db.VarChar(500)
  bio           String?
  position      String?    @db.VarChar(100)
  company       String?    @db.VarChar(200)
  experience    Int?
  industry      String?    @db.VarChar(100)
  education     String?    @db.VarChar(200)
  skills        String[]   @default([])
  level         UserLevel? @default(NEWBIE)
  points        Int?       @default(0)
  reputation    Decimal?   @default(0.0) @db.Decimal(10, 2)
  isAnonymous   Boolean?   @default(false)
  isEmailPublic Boolean?   @default(false)
  isPhonePublic Boolean?   @default(false)
  isVerified    Boolean?   @default(false)
  isActive      Boolean?   @default(true)
  isBanned      Boolean?   @default(false)
  createdAt     DateTime?  @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime?  @default(now()) @updatedAt @db.Timestamptz(6)
  lastLogin     DateTime?  @db.Timestamptz(6)

  // 关系定义
  accounts        Account[]
  sessions        Session[]
  workExperiences WorkExperience[]

  // 添加发布内容的关系
  posts      Post[]
  comments   Comment[]
  ratings    Rating[]
  interviews Interview[]
  salaries   Salary[]
  likes      Like[]
  bookmarks  Bookmark[]
  reports    Report[]

  @@map("users")
}

// 账号模型
model Account {
  id                String  @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId            String  @db.Uuid
  type              String  @db.VarChar(50)
  provider          String  @db.VarChar(50)
  providerAccountId String  @db.VarChar(100)
  refresh_token     String?
  access_token      String?
  expires_at        Int?
  token_type        String? @db.VarChar(50)
  scope             String? @db.VarChar(200)
  id_token          String?
  session_state     String? @db.VarChar(200)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId], map: "unique_provider_account")
  @@map("accounts")
}

// 会话模型
model Session {
  id           String   @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  sessionToken String   @unique @db.VarChar(255)
  userId       String   @db.Uuid
  expires      DateTime @db.Timestamptz(6)

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map("sessions")
}

// 验证令牌模型
model VerificationToken {
  identifier String   @db.VarChar(255)
  token      String   @unique @db.VarChar(255)
  expires    DateTime @db.Timestamptz(6)

  @@unique([identifier, token], map: "unique_identifier_token")
  @@map("verification_tokens")
}

// 公司模型
model Company {
  id            String       @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  name          String       @unique @db.VarChar(200)
  nameEn        String?      @unique @db.VarChar(200)
  logo          String?      @db.VarChar(500)
  description   String?
  website       String?      @db.VarChar(500)
  industry      String?      @db.VarChar(100)
  size          CompanySize?
  foundedYear   Int?
  headquarters  String?      @db.VarChar(100)
  address       String?
  phone         String?      @db.VarChar(50)
  email         String?      @db.VarChar(255)
  isVerified    Boolean?     @default(false)
  isActive      Boolean?     @default(true)
  totalRatings  Int?         @default(0)
  averageRating Decimal?     @default(0.0) @db.Decimal(3, 2)
  totalSalaries Int?         @default(0)
  totalReviews  Int?         @default(0)
  createdAt     DateTime?    @default(now()) @db.Timestamptz(6)
  updatedAt     DateTime?    @default(now()) @updatedAt @db.Timestamptz(6)

  // 关系定义
  posts      Post[]
  salaries   Salary[]
  interviews Interview[]
  ratings    Rating[]

  @@map("companies")
}

// 帖子模型
model Post {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  title        String    @db.VarChar(200)
  content      String
  excerpt      String?   @db.VarChar(500)
  type         PostType? @default(DISCUSSION)
  category     String?   @db.VarChar(50)
  tags         String[]  @default([])
  companyId    String?   @db.Uuid
  authorId     String    @db.Uuid
  isAnonymous  Boolean?  @default(false)
  isPublished  Boolean?  @default(true)
  isPinned     Boolean?  @default(false)
  isLocked     Boolean?  @default(false)
  isDeleted    Boolean?  @default(false)
  viewCount    Int?      @default(0)
  likeCount    Int?      @default(0)
  commentCount Int?      @default(0)
  shareCount   Int?      @default(0)
  createdAt    DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime? @default(now()) @updatedAt @db.Timestamptz(6)
  publishedAt  DateTime? @db.Timestamptz(6)

  // 关系定义
  author  User     @relation(fields: [authorId], references: [id], onDelete: Cascade)
  Company Company? @relation(fields: [companyId], references: [id])

  @@map("posts")
}

// 评论模型
model Comment {
  id          String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  content     String
  postId      String    @db.Uuid
  authorId    String    @db.Uuid
  parentId    String?   @db.Uuid
  isAnonymous Boolean?  @default(false)
  isDeleted   Boolean?  @default(false)
  likeCount   Int?      @default(0)
  replyCount  Int?      @default(0)
  createdAt   DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt   DateTime? @default(now()) @updatedAt @db.Timestamptz(6)

  // 关系定义
  author User @relation(fields: [authorId], references: [id], onDelete: Cascade)

  @@map("comments")
}

// 点赞模型
model Like {
  id        String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String    @db.Uuid
  postId    String?   @db.Uuid
  commentId String?   @db.Uuid
  createdAt DateTime? @default(now()) @db.Timestamptz(6)

  // 关系定义
  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([userId, commentId], map: "unique_user_comment_like")
  @@unique([userId, postId], map: "unique_user_post_like")
  @@map("likes")
}

// 收藏模型
model Bookmark {
  id        String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId    String    @db.Uuid
  postId    String    @db.Uuid
  createdAt DateTime? @default(now()) @db.Timestamptz(6)
  User      User      @relation(fields: [userId], references: [id])

  @@unique([userId, postId], map: "unique_user_post_bookmark")
  @@map("bookmarks")
}

// 薪资模型
model Salary {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  authorId     String    @db.Uuid
  companyId    String    @db.Uuid
  position     String    @db.VarChar(100)
  level        String?   @db.VarChar(50)
  department   String?   @db.VarChar(100)
  workLocation String?   @db.VarChar(100)
  workType     String?   @db.VarChar(50)
  experience   Int?
  education    String?   @db.VarChar(100)
  baseSalary   Decimal?  @db.Decimal(12, 2)
  bonus        Decimal?  @default(0) @db.Decimal(12, 2)
  stockOptions Decimal?  @default(0) @db.Decimal(12, 2)
  benefits     Decimal?  @default(0) @db.Decimal(12, 2)
  totalSalary  Decimal   @db.Decimal(12, 2)
  currency     String?   @default("CNY") @db.VarChar(10)
  salaryYear   Int
  notes        String?
  tags         String[]  @default([])
  isVerified   Boolean?  @default(false)
  isActive     Boolean?  @default(true)
  isAnonymous  Boolean?  @default(true)
  createdAt    DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime? @default(now()) @updatedAt @db.Timestamptz(6)
  User         User      @relation(fields: [authorId], references: [id])
  Company      Company   @relation(fields: [companyId], references: [id])

  @@map("salaries")
}

// 面试模型
model Interview {
  id             String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  authorId       String              @db.Uuid
  companyId      String              @db.Uuid
  position       String              @db.VarChar(100)
  department     String?             @db.VarChar(100)
  interviewType  String?             @db.VarChar(50)
  interviewRound Int?                @default(1)
  interviewDate  DateTime?           @db.Date
  duration       Int?
  difficulty     InterviewDifficulty
  result         InterviewResult
  rating         Int?
  questions      String[]
  experience     String?
  tips           String?
  notes          String?
  isActive       Boolean?            @default(true)
  isAnonymous    Boolean?            @default(true)
  createdAt      DateTime?           @default(now()) @db.Timestamptz(6)
  updatedAt      DateTime?           @default(now()) @updatedAt @db.Timestamptz(6)
  User           User                @relation(fields: [authorId], references: [id])
  Company        Company             @relation(fields: [companyId], references: [id])

  @@map("interviews")
}

// 评分模型
model Rating {
  id                   String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  authorId             String    @db.Uuid
  companyId            String    @db.Uuid
  overallRating        Decimal   @db.Decimal(2, 1)
  workLifeBalance      Int?
  compensation         Int?
  culture              Int?
  careerGrowth         Int?
  management           Int?
  title                String?   @db.VarChar(200)
  pros                 String?
  cons                 String?
  advice               String?
  isRecommended        Boolean?
  recommendationReason String?
  position             String?   @db.VarChar(100)
  department           String?   @db.VarChar(100)
  workDuration         Int?
  employmentType       String?   @db.VarChar(50)
  isActive             Boolean?  @default(true)
  isAnonymous          Boolean?  @default(true)
  isVerified           Boolean?  @default(false)
  createdAt            DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt            DateTime? @default(now()) @updatedAt @db.Timestamptz(6)
  User                 User      @relation(fields: [authorId], references: [id])
  Company              Company   @relation(fields: [companyId], references: [id])

  @@unique([authorId, companyId], map: "unique_user_company_rating")
  @@map("ratings")
}

// 举报模型
model Report {
  id           String        @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  reporterId   String        @db.Uuid
  targetType   String        @db.VarChar(50)
  targetId     String        @db.Uuid
  reason       ReportReason
  description  String?
  status       ReportStatus? @default(PENDING)
  handlerId    String?       @db.Uuid
  handlerNotes String?
  resolvedAt   DateTime?     @db.Timestamptz(6)
  createdAt    DateTime?     @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime?     @default(now()) @updatedAt @db.Timestamptz(6)
  User         User?         @relation(fields: [userId], references: [id])
  userId       String?       @db.Uuid

  @@map("reports")
}

// 工作经历模型
model WorkExperience {
  id                 String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId             String              @db.Uuid
  companyName        String              @db.VarChar(200)
  position           String              @db.VarChar(100)
  department         String?             @db.VarChar(100)
  employmentType     EmploymentType
  startDate          DateTime            @db.Date
  endDate            DateTime?           @db.Date
  isCurrent          Boolean?            @default(false)
  description        String?
  achievements       String[]
  skills             String[]
  salary             Decimal?            @db.Decimal(12, 2)
  currency           String?             @default("CNY") @db.VarChar(10)
  verificationStatus VerificationStatus? @default(PENDING)
  verifiedById       String?             @db.Uuid
  verifiedAt         DateTime?           @db.Timestamptz(6)
  isPublic           Boolean?            @default(false)
  createdAt          DateTime?           @default(now()) @db.Timestamptz(6)
  updatedAt          DateTime?           @default(now()) @updatedAt @db.Timestamptz(6)

  // 关系定义
  user  User             @relation(fields: [userId], references: [id], onDelete: Cascade)
  files ExperienceFile[]

  @@map("work_experiences")
}

// 文件模型
model File {
  id           String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  originalName String    @db.VarChar(255)
  fileName     String    @db.VarChar(255)
  filePath     String    @db.VarChar(500)
  fileSize     Int
  mimeType     String    @db.VarChar(100)
  fileHash     String    @db.VarChar(64)
  type         String    @db.VarChar(50)
  description  String?
  relatedId    String?   @db.Uuid
  relatedType  String?   @db.VarChar(50)
  uploadedById String    @db.Uuid
  isDeleted    Boolean?  @default(false)
  createdAt    DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt    DateTime? @default(now()) @updatedAt @db.Timestamptz(6)

  @@unique([fileHash, uploadedById], map: "unique_file_hash_user")
  @@map("files")
}

// 经验文件模型
model ExperienceFile {
  id                 String              @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  workExperienceId   String              @db.Uuid
  fileName           String              @db.VarChar(255)
  fileUrl            String              @db.VarChar(500)
  fileSize           Int?
  mimeType           String?             @db.VarChar(100)
  category           FileCategory
  description        String?
  verificationStatus VerificationStatus? @default(PENDING)
  verifiedById       String?             @db.Uuid
  verifiedAt         DateTime?           @db.Timestamptz(6)
  uploadedAt         DateTime?           @default(now()) @db.Timestamptz(6)
  createdAt          DateTime?           @default(now()) @db.Timestamptz(6)
  updatedAt          DateTime?           @default(now()) @db.Timestamptz(6)

  // 关系定义
  workExperience WorkExperience @relation(fields: [workExperienceId], references: [id], onDelete: Cascade)

  @@map("experience_files")
}

// 验证记录模型
model VerificationRecord {
  id         String         @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  reviewerId String         @db.Uuid
  targetType TargetType
  targetId   String         @db.Uuid
  decision   ReviewDecision
  reason     String?
  notes      String?
  createdAt  DateTime?      @default(now()) @db.Timestamptz(6)

  @@map("verification_records")
}

// 用户信誉模型
model UserCredibility {
  id                      String    @id @default(dbgenerated("gen_random_uuid()")) @db.Uuid
  userId                  String    @unique @db.Uuid
  credibilityScore        Decimal?  @default(0.0) @db.Decimal(5, 2)
  verifiedWorkExperiences Int?      @default(0)
  verifiedSalaries        Int?      @default(0)
  verifiedInterviews      Int?      @default(0)
  contributionPoints      Int?      @default(0)
  reportCount             Int?      @default(0)
  lastCalculatedAt        DateTime? @default(now()) @db.Timestamptz(6)
  createdAt               DateTime? @default(now()) @db.Timestamptz(6)
  updatedAt               DateTime? @default(now()) @updatedAt @db.Timestamptz(6)

  @@map("user_credibility")
}

// 用户等级枚举
enum UserLevel {
  NEWBIE
  ACTIVE
  SENIOR
  EXPERT
  MODERATOR
  ADMIN
}

// 公司规模枚举
enum CompanySize {
  STARTUP
  SMALL
  MEDIUM
  LARGE
  ENTERPRISE
}

// 帖子类型枚举
enum PostType {
  DISCUSSION
  QUESTION
  SHARING
  NEWS
  REVIEW
  JOB
}

// 面试难度枚举
enum InterviewDifficulty {
  EASY
  MEDIUM
  HARD
  VERY_HARD
}

// 面试结果枚举
enum InterviewResult {
  PASSED
  FAILED
  PENDING
  CANCELLED
}

// 举报原因枚举
enum ReportReason {
  SPAM
  INAPPROPRIATE
  FAKE_INFO
  HARASSMENT
  COPYRIGHT
  OTHER
}

// 举报状态枚举
enum ReportStatus {
  PENDING
  REVIEWING
  RESOLVED
  REJECTED
}

// 雇佣类型枚举
enum EmploymentType {
  FULL_TIME
  PART_TIME
  CONTRACT
  INTERNSHIP
  FREELANCE
}

// 验证状态枚举
enum VerificationStatus {
  PENDING
  APPROVED
  REJECTED
  REVOKED
}

// 文件分类枚举
enum FileCategory {
  CONTRACT
  CERTIFICATE
  PHOTO
  DOCUMENT
  OTHER
}

// 目标类型枚举
enum TargetType {
  WORK_EXPERIENCE
  EXPERIENCE_FILE
  SALARY
  INTERVIEW
}

// 审核决策枚举
enum ReviewDecision {
  APPROVED
  REJECTED
  REVOKED
  PENDING_MORE_INFO
}
