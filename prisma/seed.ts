import { PrismaClient } from '@prisma/client'
import bcrypt from 'bcryptjs'

const prisma = new PrismaClient()

async function main() {
  console.log('开始插入种子数据...')

  // 清理现有数据 (仅开发环境)
  if (process.env.NODE_ENV === 'development') {
    console.log('清理现有数据...')
    await prisma.report.deleteMany()
    await prisma.rating.deleteMany()
    await prisma.interview.deleteMany()
    await prisma.salary.deleteMany()
    await prisma.bookmark.deleteMany()
    await prisma.like.deleteMany()
    await prisma.comment.deleteMany()
    await prisma.post.deleteMany()
    await prisma.company.deleteMany()
    await prisma.session.deleteMany()
    await prisma.account.deleteMany()
    await prisma.user.deleteMany()
  }

  // 创建测试用户
  console.log('创建用户...')
  const adminPasswordHash = await bcrypt.hash('admin123', 10)
  const userPasswordHash = await bcrypt.hash('123456', 10)

  const admin = await prisma.user.create({
    data: {
      email: '<EMAIL>',
      username: 'admin',
      name: '系统管理员',
      password: adminPasswordHash,
      position: '系统管理员',
      company: 'WorkMates',
      experience: 5,
      industry: 'IT',
      level: 'ADMIN',
      points: 10000,
      reputation: 100.0,
      isVerified: true,
      isActive: true,
    },
  })

  const users = await Promise.all([
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'zhangsan',
        name: '张三',
        password: userPasswordHash,
        position: '前端开发工程师',
        company: '阿里巴巴',
        experience: 3,
        industry: 'IT',
        level: 'ACTIVE',
        points: 1500,
        reputation: 4.5,
        isVerified: true,
        isActive: true,
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'lisi',
        name: '李四',
        password: userPasswordHash,
        position: '后端开发工程师',
        company: '腾讯',
        experience: 5,
        industry: 'IT',
        level: 'SENIOR',
        points: 2800,
        reputation: 4.8,
        isVerified: true,
        isActive: true,
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'wangwu',
        name: '王五',
        password: userPasswordHash,
        position: '产品经理',
        company: '字节跳动',
        experience: 4,
        industry: 'IT',
        level: 'ACTIVE',
        points: 2200,
        reputation: 4.2,
        isVerified: true,
        isActive: true,
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'zhaoliu',
        name: '赵六',
        password: userPasswordHash,
        position: 'UI设计师',
        company: '美团',
        experience: 2,
        industry: 'IT',
        level: 'ACTIVE',
        points: 800,
        reputation: 3.8,
        isVerified: false,
        isActive: true,
      },
    }),
    prisma.user.create({
      data: {
        email: '<EMAIL>',
        username: 'chenqi',
        name: '陈七',
        password: userPasswordHash,
        position: '数据分析师',
        company: '小米',
        experience: 3,
        industry: 'IT',
        level: 'ACTIVE',
        points: 1200,
        reputation: 4.0,
        isVerified: false,
        isActive: true,
      },
    }),
  ])

  // 创建企业
  console.log('创建企业...')
  const companies = await Promise.all([
    prisma.company.create({
      data: {
        name: '阿里巴巴集团',
        nameEn: 'Alibaba Group',
        description: '阿里巴巴集团控股有限公司是一家中国多元化科技公司，专注于电子商务、云计算、数字媒体和娱乐、创新举措等业务。',
        website: 'https://www.alibaba.com',
        industry: 'IT',
        size: 'ENTERPRISE',
        foundedYear: 1999,
        headquarters: '杭州',
        isVerified: true,
        isActive: true,
      },
    }),
    prisma.company.create({
      data: {
        name: '腾讯控股有限公司',
        nameEn: 'Tencent Holdings Limited',
        description: '腾讯控股有限公司是一家中国的投资控股公司，其子公司主要从事互联网相关服务和产品、娱乐、人工智能和其他技术。',
        website: 'https://www.tencent.com',
        industry: 'IT',
        size: 'ENTERPRISE',
        foundedYear: 1998,
        headquarters: '深圳',
        isVerified: true,
        isActive: true,
      },
    }),
    prisma.company.create({
      data: {
        name: '字节跳动有限公司',
        nameEn: 'ByteDance Ltd.',
        description: '字节跳动是一家全球化的互联网技术公司，致力于用技术丰富人们的生活。',
        website: 'https://www.bytedance.com',
        industry: 'IT',
        size: 'ENTERPRISE',
        foundedYear: 2012,
        headquarters: '北京',
        isVerified: true,
        isActive: true,
      },
    }),
    prisma.company.create({
      data: {
        name: '美团',
        nameEn: 'Meituan',
        description: '美团是中国领先的生活服务电子商务平台，致力于帮助消费者发现最好的生活服务，帮助商家获得更多客户。',
        website: 'https://www.meituan.com',
        industry: 'IT',
        size: 'LARGE',
        foundedYear: 2010,
        headquarters: '北京',
        isVerified: true,
        isActive: true,
      },
    }),
    prisma.company.create({
      data: {
        name: '小米集团',
        nameEn: 'Xiaomi Corporation',
        description: '小米集团是一家专注于智能硬件和电子产品研发的移动互联网公司，同时也是一家专注于高端智能手机、互联网电视以及智能家居生态链建设的创新型科技企业。',
        website: 'https://www.mi.com',
        industry: 'IT',
        size: 'LARGE',
        foundedYear: 2010,
        headquarters: '北京',
        isVerified: true,
        isActive: true,
      },
    }),
    prisma.company.create({
      data: {
        name: '华为技术有限公司',
        nameEn: 'Huawei Technologies Co., Ltd.',
        description: '华为是全球领先的信息与通信技术(ICT)解决方案供应商，专注于ICT领域，坚持稳健经营、持续创新、开放合作。',
        website: 'https://www.huawei.com',
        industry: 'IT',
        size: 'ENTERPRISE',
        foundedYear: 1987,
        headquarters: '深圳',
        isVerified: true,
        isActive: true,
      },
    }),
  ])

  // 创建帖子
  console.log('创建帖子...')
  const posts = await Promise.all([
    prisma.post.create({
      data: {
        title: '阿里巴巴前端面试经验分享',
        content: `刚刚结束了阿里巴巴前端工程师的面试，想和大家分享一下经验。

## 面试流程
1. 一面：基础技术面试
2. 二面：项目经验和算法
3. 三面：系统设计和价值观

## 技术问题
主要考察了React、JavaScript基础、CSS布局、网络协议等知识点。

## 建议
1. 扎实的基础很重要
2. 项目经验要能深入讲解
3. 保持学习新技术的热情`,
        excerpt: '刚刚结束了阿里巴巴前端工程师的面试，想和大家分享一下经验。',
        type: 'SHARING',
        category: '面试经验',
        tags: ['前端', '阿里巴巴', '面试'],
        companyId: companies[0].id, // 阿里巴巴
        authorId: users[0].id, // 张三
        isPublished: true,
        viewCount: 158,
        likeCount: 23,
        commentCount: 8,
        publishedAt: new Date(),
      },
    }),
    prisma.post.create({
      data: {
        title: '腾讯的工作环境怎么样？',
        content: `最近在考虑跳槽到腾讯，想了解一下腾讯的工作环境和企业文化。

有在腾讯工作过的朋友可以分享一下吗？主要想了解：
1. 工作强度如何
2. 团队氛围
3. 晋升机制
4. 福利待遇

谢谢大家！`,
        excerpt: '最近在考虑跳槽到腾讯，想了解一下腾讯的工作环境和企业文化。',
        type: 'QUESTION',
        category: '企业咨询',
        tags: ['腾讯', '工作环境'],
        companyId: companies[1].id, // 腾讯
        authorId: users[1].id, // 李四
        isPublished: true,
        viewCount: 89,
        likeCount: 12,
        commentCount: 15,
        publishedAt: new Date(),
      },
    }),
  ])

  console.log('种子数据插入完成！')
}

main()
  .then(async () => {
    await prisma.$disconnect()
  })
  .catch(async (e) => {
    console.error(e)
    await prisma.$disconnect()
    process.exit(1)
  })