-- WorkMates 数据库监控和性能分析脚本
-- 提供实时监控、性能分析和业务指标统计功能

-- ================================
-- 实时监控视图
-- ================================

-- 系统活动监控视图
CREATE OR REPLACE VIEW system_activity_monitor AS
SELECT
    'Active Connections' as metric_name,
    COUNT(*) as current_value,
    100 as threshold_warning,
    200 as threshold_critical
FROM pg_stat_activity
WHERE
    state = 'active'
UNION ALL
SELECT
    'Database Size' as metric_name,
    pg_database_size (current_database ()) / 1024 / 1024 as current_value, -- MB
    1000 as threshold_warning, -- 1GB
    5000 as threshold_critical -- 5GB
UNION ALL
SELECT
    'Long Running Queries' as metric_name,
    COUNT(*) as current_value,
    5 as threshold_warning,
    10 as threshold_critical
FROM pg_stat_activity
WHERE
    state = 'active'
    AND query_start < NOW() - INTERVAL '1 minute'
UNION ALL
SELECT
    'Locked Queries' as metric_name,
    COUNT(*) as current_value,
    3 as threshold_warning,
    10 as threshold_critical
FROM pg_stat_activity
WHERE
    wait_event_type = 'Lock';

-- 表级性能监控视图
CREATE OR REPLACE VIEW table_performance_monitor AS
SELECT 
    schemaname,
    tablename,
    seq_scan as sequential_scans,
    seq_tup_read as sequential_tuples_read,
    idx_scan as index_scans,
    idx_tup_fetch as index_tuples_fetched,
    n_tup_ins as inserts,
    n_tup_upd as updates,
    n_tup_del as deletes,
    n_live_tup as live_tuples,
    n_dead_tup as dead_tuples,
    CASE 
        WHEN n_live_tup > 0 THEN ROUND((n_dead_tup::float / n_live_tup::float) * 100, 2)
        ELSE 0 
    END as dead_tuple_percentage,
    last_vacuum,
    last_autovacuum,
    last_analyze,
    last_autoanalyze
FROM pg_stat_user_tables
ORDER BY n_live_tup DESC;

-- 索引使用率监控视图
CREATE OR REPLACE VIEW index_usage_monitor AS
SELECT 
    schemaname,
    tablename,
    indexname,
    idx_tup_read as index_reads,
    idx_tup_fetch as index_fetches,
    CASE 
        WHEN idx_tup_read > 0 THEN ROUND((idx_tup_fetch::float / idx_tup_read::float) * 100, 2)
        ELSE 0 
    END as fetch_ratio_percentage,
    pg_size_pretty(pg_relation_size(indexrelid)) as index_size
FROM pg_stat_user_indexes
ORDER BY idx_tup_read DESC;

-- ================================
-- 业务指标监控
-- ================================

-- 每日业务指标视图
CREATE OR REPLACE VIEW daily_business_metrics AS
SELECT CURRENT_DATE as date,

-- 用户指标
(SELECT COUNT(*) FROM users WHERE "createdAt"::date = CURRENT_DATE) as new_users_today,
    (SELECT COUNT(*) FROM users WHERE "lastLogin"::date = CURRENT_DATE) as active_users_today,
    (SELECT COUNT(*) FROM users WHERE "isActive" = true) as total_active_users,

-- 内容指标
(SELECT COUNT(*) FROM posts WHERE "createdAt"::date = CURRENT_DATE AND "isPublished" = true) as new_posts_today,
    (SELECT COUNT(*) FROM comments WHERE "createdAt"::date = CURRENT_DATE AND "isDeleted" = false) as new_comments_today,
    (SELECT COUNT(*) FROM likes WHERE "createdAt"::date = CURRENT_DATE) as new_likes_today,

-- 数据贡献指标
(SELECT COUNT(*) FROM salaries WHERE "createdAt"::date = CURRENT_DATE) as new_salaries_today,
    (SELECT COUNT(*) FROM interviews WHERE "createdAt"::date = CURRENT_DATE) as new_interviews_today,
    (SELECT COUNT(*) FROM ratings WHERE "createdAt"::date = CURRENT_DATE) as new_ratings_today,

-- 质量指标
(SELECT COUNT(*) FROM reports WHERE "createdAt"::date = CURRENT_DATE) as new_reports_today,
    (SELECT COUNT(*) FROM reports WHERE "status" = 'PENDING') as pending_reports;

-- 热门内容分析视图

CREATE OR REPLACE VIEW trending_content_analysis AS
SELECT 
    'posts' as content_type,
    p.id as content_id,
    p.title,
    u.username as author,
    c.name as company,
    p."viewCount" as views,
    p."likeCount" as likes,
    p."commentCount" as comments,
    p."createdAt",
    -- 热度分数计算
    (p."likeCount" * 3 + p."commentCount" * 2 + p."viewCount" * 0.1) as engagement_score
FROM posts p
JOIN users u ON p."authorId" = u.id
LEFT JOIN companies c ON p."companyId" = c.id
WHERE p."isPublished" = true 
  AND p."createdAt" >= NOW() - INTERVAL '7 days'

UNION ALL

SELECT 
    'companies' as content_type,
    c.id as content_id,
    c.name as title,
    NULL as author,
    c.name as company,
    0 as views,
    c."totalRatings" as likes,
    0 as comments,
    c."createdAt",
    (c."totalRatings" * 2 + c."totalSalaries" * 1.5) as engagement_score
FROM companies c
WHERE c."isActive" = true

ORDER BY engagement_score DESC
LIMIT 20;

-- ================================
-- 性能分析函数
-- ================================

-- 慢查询分析函数
CREATE OR REPLACE FUNCTION analyze_slow_queries(duration_threshold_ms INTEGER DEFAULT 1000)
RETURNS TABLE(
    query_text TEXT,
    total_exec_time NUMERIC,
    mean_exec_time NUMERIC,
    calls BIGINT,
    percentage_total_time NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        LEFT(query, 100) as query_text,
        ROUND(total_exec_time::numeric, 2) as total_exec_time,
        ROUND(mean_exec_time::numeric, 2) as mean_exec_time,
        calls,
        ROUND((total_exec_time / SUM(total_exec_time) OVER()) * 100, 2) as percentage_total_time
    FROM pg_stat_statements
    WHERE mean_exec_time > duration_threshold_ms
      AND query NOT LIKE '%pg_stat%'
    ORDER BY mean_exec_time DESC
    LIMIT 20;
END;
$$ LANGUAGE plpgsql;

-- 锁分析函数
CREATE OR REPLACE FUNCTION analyze_locks()
RETURNS TABLE(
    blocked_query TEXT,
    blocking_query TEXT,
    blocked_pid INTEGER,
    blocking_pid INTEGER,
    lock_duration INTERVAL
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        blocked.query as blocked_query,
        blocking.query as blocking_query,
        blocked.pid as blocked_pid,
        blocking.pid as blocking_pid,
        NOW() - blocked.query_start as lock_duration
    FROM pg_stat_activity blocked
    JOIN pg_locks blocked_locks ON blocked.pid = blocked_locks.pid
    JOIN pg_locks blocking_locks ON blocked_locks.locktype = blocking_locks.locktype
        AND blocked_locks.database IS NOT DISTINCT FROM blocking_locks.database
        AND blocked_locks.relation IS NOT DISTINCT FROM blocking_locks.relation
        AND blocked_locks.page IS NOT DISTINCT FROM blocking_locks.page
        AND blocked_locks.tuple IS NOT DISTINCT FROM blocking_locks.tuple
        AND blocked_locks.virtualxid IS NOT DISTINCT FROM blocking_locks.virtualxid
        AND blocked_locks.transactionid IS NOT DISTINCT FROM blocking_locks.transactionid
        AND blocked_locks.classid IS NOT DISTINCT FROM blocking_locks.classid
        AND blocked_locks.objid IS NOT DISTINCT FROM blocking_locks.objid
        AND blocked_locks.objsubid IS NOT DISTINCT FROM blocking_locks.objsubid
        AND blocked_locks.pid != blocking_locks.pid
    JOIN pg_stat_activity blocking ON blocking_locks.pid = blocking.pid
    WHERE NOT blocked_locks.granted;
END;
$$ LANGUAGE plpgsql;

-- 连接池分析函数
CREATE OR REPLACE FUNCTION analyze_connection_pool()
RETURNS TABLE(
    state TEXT,
    count BIGINT,
    percentage NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        COALESCE(state, 'unknown') as state,
        COUNT(*) as count,
        ROUND((COUNT(*) * 100.0 / SUM(COUNT(*)) OVER()), 2) as percentage
    FROM pg_stat_activity
    GROUP BY state
    ORDER BY count DESC;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 业务数据分析函数
-- ================================

-- 用户活跃度分析函数
CREATE OR REPLACE FUNCTION analyze_user_activity(days_back INTEGER DEFAULT 30)
RETURNS TABLE(
    date DATE,
    daily_active_users BIGINT,
    new_users BIGINT,
    posts_created BIGINT,
    comments_created BIGINT
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        d.date,
        COALESCE(dau.count, 0) as daily_active_users,
        COALESCE(nu.count, 0) as new_users,
        COALESCE(pc.count, 0) as posts_created,
        COALESCE(cc.count, 0) as comments_created
    FROM (
        SELECT generate_series(
            CURRENT_DATE - INTERVAL days_back || ' days',
            CURRENT_DATE,
            '1 day'::interval
        )::date as date
    ) d
    LEFT JOIN (
        SELECT 
            "lastLogin"::date as date,
            COUNT(DISTINCT id) as count
        FROM users 
        WHERE "lastLogin" >= CURRENT_DATE - INTERVAL days_back || ' days'
        GROUP BY "lastLogin"::date
    ) dau ON d.date = dau.date
    LEFT JOIN (
        SELECT 
            "createdAt"::date as date,
            COUNT(*) as count
        FROM users 
        WHERE "createdAt" >= CURRENT_DATE - INTERVAL days_back || ' days'
        GROUP BY "createdAt"::date
    ) nu ON d.date = nu.date
    LEFT JOIN (
        SELECT 
            "createdAt"::date as date,
            COUNT(*) as count
        FROM posts 
        WHERE "createdAt" >= CURRENT_DATE - INTERVAL days_back || ' days'
          AND "isPublished" = true
        GROUP BY "createdAt"::date
    ) pc ON d.date = pc.date
    LEFT JOIN (
        SELECT 
            "createdAt"::date as date,
            COUNT(*) as count
        FROM comments 
        WHERE "createdAt" >= CURRENT_DATE - INTERVAL days_back || ' days'
          AND "isDeleted" = false
        GROUP BY "createdAt"::date
    ) cc ON d.date = cc.date
    ORDER BY d.date;
END;
$$ LANGUAGE plpgsql;

-- 企业数据分析函数
CREATE OR REPLACE FUNCTION analyze_company_data()
RETURNS TABLE(
    company_name TEXT,
    total_salaries BIGINT,
    avg_salary NUMERIC,
    total_interviews BIGINT,
    total_ratings BIGINT,
    avg_rating NUMERIC,
    engagement_score NUMERIC
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        c.name as company_name,
        COUNT(DISTINCT s.id) as total_salaries,
        ROUND(AVG(s."totalSalary"), 0) as avg_salary,
        COUNT(DISTINCT i.id) as total_interviews,
        COUNT(DISTINCT r.id) as total_ratings,
        ROUND(AVG(r.overall), 2) as avg_rating,
        (COUNT(DISTINCT s.id) * 2 + COUNT(DISTINCT i.id) * 1.5 + COUNT(DISTINCT r.id) * 1) as engagement_score
    FROM companies c
    LEFT JOIN salaries s ON c.id = s."companyId"
    LEFT JOIN interviews i ON c.id = i."companyId"
    LEFT JOIN ratings r ON c.id = r."companyId"
    WHERE c."isActive" = true
    GROUP BY c.id, c.name
    HAVING COUNT(DISTINCT s.id) > 0 OR COUNT(DISTINCT i.id) > 0 OR COUNT(DISTINCT r.id) > 0
    ORDER BY engagement_score DESC;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 监控报警函数
-- ================================

-- 系统健康检查函数
CREATE OR REPLACE FUNCTION system_health_check()
RETURNS TABLE(
    check_category TEXT,
    check_name TEXT,
    status TEXT,
    current_value NUMERIC,
    threshold_value NUMERIC,
    message TEXT
) AS $$
BEGIN
    -- 检查活跃连接数
    RETURN QUERY
    SELECT 
        'Database' as check_category,
        'Active Connections' as check_name,
        CASE 
            WHEN COUNT(*) > 100 THEN 'CRITICAL'
            WHEN COUNT(*) > 50 THEN 'WARNING'
            ELSE 'OK'
        END as status,
        COUNT(*)::numeric as current_value,
        100::numeric as threshold_value,
        'Active database connections: ' || COUNT(*) as message
    FROM pg_stat_activity 
    WHERE state = 'active';
    
    -- 检查数据库大小
    RETURN QUERY
    SELECT 
        'Database' as check_category,
        'Database Size' as check_name,
        CASE 
            WHEN pg_database_size(current_database()) > ********** THEN 'CRITICAL' -- 5GB
            WHEN pg_database_size(current_database()) > ********** THEN 'WARNING'  -- 1GB
            ELSE 'OK'
        END as status,
        (pg_database_size(current_database()) / 1024 / 1024)::numeric as current_value,
        5120::numeric as threshold_value, -- 5GB in MB
        'Database size: ' || pg_size_pretty(pg_database_size(current_database())) as message;
    
    -- 检查长时间运行的查询
    RETURN QUERY
    SELECT 
        'Performance' as check_category,
        'Long Running Queries' as check_name,
        CASE 
            WHEN COUNT(*) > 5 THEN 'CRITICAL'
            WHEN COUNT(*) > 2 THEN 'WARNING'
            ELSE 'OK'
        END as status,
        COUNT(*)::numeric as current_value,
        5::numeric as threshold_value,
        'Queries running > 5 minutes: ' || COUNT(*) as message
    FROM pg_stat_activity 
    WHERE state = 'active' 
      AND query_start < NOW() - INTERVAL '5 minutes';
    
    -- 检查死锁
    RETURN QUERY
    SELECT 
        'Performance' as check_category,
        'Deadlocks' as check_name,
        CASE 
            WHEN COALESCE(deadlocks, 0) > 10 THEN 'CRITICAL'
            WHEN COALESCE(deadlocks, 0) > 5 THEN 'WARNING'
            ELSE 'OK'
        END as status,
        COALESCE(deadlocks, 0)::numeric as current_value,
        10::numeric as threshold_value,
        'Recent deadlocks: ' || COALESCE(deadlocks, 0) as message
    FROM pg_stat_database 
    WHERE datname = current_database();
    
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 报告生成函数
-- ================================

-- 生成日常报告
CREATE OR REPLACE FUNCTION generate_daily_report(report_date DATE DEFAULT CURRENT_DATE)
RETURNS TEXT AS $$
DECLARE
    report_content TEXT := '';
    temp_line TEXT;
BEGIN
    report_content := '# WorkMates 日常报告 - ' || report_date || E'\n\n';
    
    -- 系统健康状态
    report_content := report_content || '## 系统健康状态' || E'\n';
    FOR temp_line IN 
        SELECT '- ' || check_name || ': ' || status || ' (' || message || ')'
        FROM system_health_check()
    LOOP
        report_content := report_content || temp_line || E'\n';
    END LOOP;
    
    -- 业务指标
    report_content := report_content || E'\n## 业务指标' || E'\n';
    SELECT INTO temp_line
        '- 新用户: ' || new_users_today || 
        ', 活跃用户: ' || active_users_today ||
        ', 新帖子: ' || new_posts_today ||
        ', 新评论: ' || new_comments_today ||
        ', 新薪资数据: ' || new_salaries_today
    FROM daily_business_metrics;
    report_content := report_content || temp_line || E'\n';
    
    -- 性能指标
    report_content := report_content || E'\n## 性能指标' || E'\n';
    FOR temp_line IN 
        SELECT '- 慢查询: ' || query_text || ' (平均: ' || mean_exec_time || 'ms)'
        FROM analyze_slow_queries(500)
        LIMIT 5
    LOOP
        report_content := report_content || temp_line || E'\n';
    END LOOP;
    
    report_content := report_content || E'\n报告生成时间: ' || NOW();
    
    -- 保存报告到日志
    INSERT INTO system_logs (operation_type, description, created_at)
    VALUES ('DAILY_REPORT', 'Daily report generated for ' || report_date, NOW());
    
    RETURN report_content;
    
EXCEPTION
    WHEN OTHERS THEN
        INSERT INTO system_logs (operation_type, description, created_at, error_message)
        VALUES ('DAILY_REPORT', 'Daily report generation failed', NOW(), SQLERRM);
        
        RETURN '报告生成失败: ' || SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- ================================
-- 实用监控查询
-- ================================

/*
-- 实时监控查询示例：

-- 1. 查看系统活动状态
SELECT * FROM system_activity_monitor;

-- 2. 查看表性能状态
SELECT * FROM table_performance_monitor WHERE dead_tuple_percentage > 10;

-- 3. 查看索引使用情况
SELECT * FROM index_usage_monitor WHERE index_reads = 0;

-- 4. 查看每日业务指标
SELECT * FROM daily_business_metrics;

-- 5. 分析热门内容
SELECT * FROM trending_content_analysis LIMIT 10;

-- 6. 分析慢查询
SELECT * FROM analyze_slow_queries(1000);

-- 7. 检查锁状态
SELECT * FROM analyze_locks();

-- 8. 分析连接池
SELECT * FROM analyze_connection_pool();

-- 9. 分析用户活跃度
SELECT * FROM analyze_user_activity(7);

-- 10. 分析企业数据
SELECT * FROM analyze_company_data() LIMIT 10;

-- 11. 系统健康检查
SELECT * FROM system_health_check() WHERE status != 'OK';

-- 12. 生成日常报告
SELECT generate_daily_report();
*/

-- ================================
-- 自动化监控任务
-- ================================

-- 创建监控统计表
CREATE TABLE IF NOT EXISTS monitoring_stats (
    id SERIAL PRIMARY KEY,
    metric_name VARCHAR(100) NOT NULL,
    metric_value NUMERIC NOT NULL,
    metric_unit VARCHAR(20),
    recorded_at TIMESTAMP
    WITH
        TIME ZONE DEFAULT NOW(),
        INDEX idx_monitoring_stats_metric (metric_name),
        INDEX idx_monitoring_stats_recorded_at (recorded_at)
);

-- 记录监控数据的函数
CREATE OR REPLACE FUNCTION record_monitoring_stats()
RETURNS VOID AS $$
BEGIN
    -- 记录基本系统指标
    INSERT INTO monitoring_stats (metric_name, metric_value, metric_unit)
    SELECT 
        metric_name,
        current_value,
        CASE 
            WHEN metric_name LIKE '%Size%' THEN 'MB'
            WHEN metric_name LIKE '%Connections%' THEN 'count'
            ELSE 'count'
        END
    FROM system_activity_monitor;
    
    -- 记录业务指标
    INSERT INTO monitoring_stats (metric_name, metric_value, metric_unit)
    SELECT 
        'new_users_today',
        new_users_today,
        'count'
    FROM daily_business_metrics
    
    UNION ALL
    
    SELECT 
        'active_users_today',
        active_users_today,
        'count'
    FROM daily_business_metrics;
    
END;
$$ LANGUAGE plpgsql;