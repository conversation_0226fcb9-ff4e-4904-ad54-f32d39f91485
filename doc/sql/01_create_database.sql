-- WorkMates 数据库初始化脚本
-- 创建数据库和基础配置

-- 创建数据库 (如果使用PostgreSQL)
-- CREATE DATABASE workmates;

-- 连接到WorkMates数据库
-- \c workmates;

-- 创建UUID扩展 (PostgreSQL需要)
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- 创建pgcrypto扩展 (用于密码加密)
CREATE EXTENSION IF NOT EXISTS pgcrypto;

-- 创建中文全文搜索配置 (可选)
-- CREATE TEXT SEARCH CONFIGURATION chinese (COPY = simple);

-- 设置时区
SET timezone = 'Asia/Shanghai';

-- 创建数据库用户 (生产环境使用)
-- CREATE USER workmates_user WITH PASSWORD 'your_secure_password';
-- GRANT ALL PRIVILEGES ON DATABASE workmates TO workmates_user;

-- 数据库编码设置
-- ALTER DATABASE workmates SET client_encoding TO 'utf8';
-- ALTER DATABASE workmates SET default_transaction_isolation TO 'read committed';
-- ALTER DATABASE workmates SET timezone TO 'Asia/Shanghai';

-- 创建应用级别的 Schema
-- CREATE SCHEMA IF NOT EXISTS workmates;
-- ALTER USER workmates_user SET search_path TO workmates, public; 