-- WorkMates 工作经历功能迁移脚本
-- 添加工作经历管理相关表和功能

BEGIN;

-- ================================
-- 工作经历相关的枚举类型
-- ================================

-- 就业类型枚举
CREATE TYPE "EmploymentType" AS ENUM (
  'FULL_TIME',
  'PART_TIME',
  'CONTRACT',
  'INTERNSHIP'
);

-- 审核状态枚举
CREATE TYPE "VerificationStatus" AS ENUM (
  'PENDING',
  'APPROVED',
  'REJECTED',
  'REVOKED'
);

-- 文件类别枚举
CREATE TYPE "FileCategory" AS ENUM (
  'CONTRACT',
  'CERTIFICATE',
  'PHOTO',
  'DOCUMENT',
  'OTHER'
);

-- 审核决定枚举
CREATE TYPE "VerificationDecision" AS ENUM (
  'APPROVED',
  'REJECTED',
  'REVOKED',
  'PENDING_MORE_INFO'
);

-- 审核目标类型枚举
CREATE TYPE "VerificationTargetType" AS ENUM (
  'WORK_EXPERIENCE',
  'EXPERIENCE_FILE',
  'SALARY',
  'INTERVIEW'
);

-- ================================
-- 工作经历表
-- ================================
CREATE TABLE IF NOT EXISTS "work_experiences" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL,

-- 基本信息
"companyName" VARCHAR(200) NOT NULL,
"position" VARCHAR(100) NOT NULL,
"department" VARCHAR(100),
"employmentType" "EmploymentType" DEFAULT 'FULL_TIME',

-- 时间信息
"startDate" DATE NOT NULL,
"endDate" DATE,
"isCurrent" BOOLEAN DEFAULT false,

-- 工作内容
"description" TEXT,
    "responsibilities" TEXT[] DEFAULT '{}',
    "achievements" TEXT[] DEFAULT '{}',
    "skillsUsed" TEXT[] DEFAULT '{}',

-- 薪资信息（可选）
"salaryRange" VARCHAR(50),
"salaryCurrency" VARCHAR(10) DEFAULT 'CNY',

-- 联系信息（可选）
"supervisorName" VARCHAR(100), "supervisorContact" VARCHAR(100),

-- 审核状态
"verificationStatus" "VerificationStatus" DEFAULT 'PENDING',
"verificationScore" DECIMAL(3, 2) DEFAULT 0.00,

-- 元数据
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "verifiedAt" TIMESTAMP
WITH
    TIME ZONE,
    "verifiedBy" UUID,

-- 外键约束
CONSTRAINT fk_work_experiences_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,
CONSTRAINT fk_work_experiences_verifier FOREIGN KEY ("verifiedBy") REFERENCES "users" ("id") ON DELETE SET NULL,

-- 检查约束
CONSTRAINT check_work_date_range CHECK ("startDate" <= COALESCE("endDate", CURRENT_DATE)),
    CONSTRAINT check_verification_score CHECK ("verificationScore" >= 0 AND "verificationScore" <= 1)
);

-- ================================
-- 工作经历文件表
-- ================================
CREATE TABLE IF NOT EXISTS "experience_files" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "workExperienceId" UUID NOT NULL,

-- 文件信息
"fileName" VARCHAR(255) NOT NULL,
"fileOriginalName" VARCHAR(255) NOT NULL,
"filePath" VARCHAR(500) NOT NULL,
"fileSize" BIGINT NOT NULL,
"fileType" VARCHAR(100) NOT NULL,
"fileCategory" "FileCategory" NOT NULL,

-- 文件描述
"title" VARCHAR(200), "description" TEXT,

-- 审核状态
"verificationStatus" "VerificationStatus" DEFAULT 'PENDING',
"isPublic" BOOLEAN DEFAULT false,

-- 元数据
"uploadedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "verifiedAt" TIMESTAMP
WITH
    TIME ZONE,
    "verifiedBy" UUID,

-- 外键约束
CONSTRAINT fk_experience_files_work_experience FOREIGN KEY ("workExperienceId") REFERENCES "work_experiences" ("id") ON DELETE CASCADE,
CONSTRAINT fk_experience_files_verifier FOREIGN KEY ("verifiedBy") REFERENCES "users" ("id") ON DELETE SET NULL,

-- 检查约束
CONSTRAINT check_file_size CHECK ("fileSize" > 0 AND "fileSize" <= 10485760) -- 最大10MB
);

-- ================================
-- 审核记录表
-- ================================
CREATE TABLE IF NOT EXISTS "verification_records" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),

-- 审核对象
"targetType" "VerificationTargetType" NOT NULL,
"targetId" UUID NOT NULL,

-- 审核信息
"reviewerId" UUID NOT NULL,
"previousStatus" VARCHAR(20),
"newStatus" VARCHAR(20) NOT NULL,

-- 审核结果
"decision" "VerificationDecision" NOT NULL,
"confidenceLevel" DECIMAL(3, 2) DEFAULT 0.00,

-- 审核详情
"reviewNotes" TEXT,
    "issuesFound" TEXT[] DEFAULT '{}',
    "requiredActions" TEXT[] DEFAULT '{}',

-- 时间信息
"createdAt" TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- 外键约束
CONSTRAINT fk_verification_records_reviewer FOREIGN KEY ("reviewerId") REFERENCES "users" ("id") ON DELETE CASCADE,

-- 检查约束
CONSTRAINT check_confidence_level CHECK ("confidenceLevel" >= 0 AND "confidenceLevel" <= 1)
);

-- ================================
-- 用户信誉分数表
-- ================================
CREATE TABLE IF NOT EXISTS "user_credibility" (
    "id" UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    "userId" UUID NOT NULL UNIQUE,

-- 信誉分数
"overallScore" DECIMAL(3, 2) DEFAULT 0.00,
"workExperienceScore" DECIMAL(3, 2) DEFAULT 0.00,
"salaryContributionScore" DECIMAL(3, 2) DEFAULT 0.00,
"interviewContributionScore" DECIMAL(3, 2) DEFAULT 0.00,

-- 验证统计
"verifiedExperiencesCount" INTEGER DEFAULT 0,
"verifiedFilesCount" INTEGER DEFAULT 0,
"verifiedSalariesCount" INTEGER DEFAULT 0,
"verifiedInterviewsCount" INTEGER DEFAULT 0,

-- 贡献统计
"totalContributionsCount" INTEGER DEFAULT 0,
"helpfulContributionsCount" INTEGER DEFAULT 0,
"flaggedContributionsCount" INTEGER DEFAULT 0,

-- 时间信息
"createdAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "updatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    "lastCalculatedAt" TIMESTAMP
WITH
    TIME ZONE DEFAULT CURRENT_TIMESTAMP,

-- 外键约束
CONSTRAINT fk_user_credibility_user FOREIGN KEY ("userId") REFERENCES "users" ("id") ON DELETE CASCADE,

-- 检查约束
CONSTRAINT check_overall_score CHECK ("overallScore" >= 0 AND "overallScore" <= 1),
    CONSTRAINT check_work_experience_score CHECK ("workExperienceScore" >= 0 AND "workExperienceScore" <= 1),
    CONSTRAINT check_salary_contribution_score CHECK ("salaryContributionScore" >= 0 AND "salaryContributionScore" <= 1),
    CONSTRAINT check_interview_contribution_score CHECK ("interviewContributionScore" >= 0 AND "interviewContributionScore" <= 1)
);

-- ================================
-- 索引创建
-- ================================

-- 工作经历表索引
CREATE INDEX IF NOT EXISTS idx_work_experiences_user_id ON "work_experiences" ("userId");

CREATE INDEX IF NOT EXISTS idx_work_experiences_company ON "work_experiences" ("companyName");

CREATE INDEX IF NOT EXISTS idx_work_experiences_position ON "work_experiences" ("position");

CREATE INDEX IF NOT EXISTS idx_work_experiences_verification_status ON "work_experiences" ("verificationStatus");

CREATE INDEX IF NOT EXISTS idx_work_experiences_date_range ON "work_experiences" ("startDate", "endDate");

CREATE INDEX IF NOT EXISTS idx_work_experiences_current ON "work_experiences" ("isCurrent")
WHERE
    "isCurrent" = true;

-- 文件表索引
CREATE INDEX IF NOT EXISTS idx_experience_files_work_experience_id ON "experience_files" ("workExperienceId");

CREATE INDEX IF NOT EXISTS idx_experience_files_verification_status ON "experience_files" ("verificationStatus");

CREATE INDEX IF NOT EXISTS idx_experience_files_category ON "experience_files" ("fileCategory");

CREATE INDEX IF NOT EXISTS idx_experience_files_public ON "experience_files" ("isPublic")
WHERE
    "isPublic" = true;

-- 审核记录表索引
CREATE INDEX IF NOT EXISTS idx_verification_records_target ON "verification_records" ("targetType", "targetId");

CREATE INDEX IF NOT EXISTS idx_verification_records_reviewer ON "verification_records" ("reviewerId");

CREATE INDEX IF NOT EXISTS idx_verification_records_created_at ON "verification_records" ("createdAt");

-- 用户信誉表索引
CREATE INDEX IF NOT EXISTS idx_user_credibility_user_id ON "user_credibility" ("userId");

CREATE INDEX IF NOT EXISTS idx_user_credibility_overall_score ON "user_credibility" ("overallScore" DESC);

-- ================================
-- 触发器
-- ================================

-- 自动更新updated_at字段
CREATE TRIGGER trigger_work_experiences_updated_at
    BEFORE UPDATE ON "work_experiences"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

CREATE TRIGGER trigger_user_credibility_updated_at
    BEFORE UPDATE ON "user_credibility"
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- ================================
-- 信誉计算函数
-- ================================

-- 计算用户信誉分数的函数
CREATE OR REPLACE FUNCTION calculate_user_credibility(p_user_id UUID)
RETURNS VOID AS $$
DECLARE
    work_exp_score DECIMAL(3,2) := 0;
    salary_contrib_score DECIMAL(3,2) := 0;
    interview_contrib_score DECIMAL(3,2) := 0;
    overall_score DECIMAL(3,2) := 0;
    verified_exp_count INTEGER := 0;
    verified_files_count INTEGER := 0;
    verified_salaries_count INTEGER := 0;
    verified_interviews_count INTEGER := 0;
BEGIN
    -- 计算工作经历验证分数
    SELECT COUNT(*) INTO verified_exp_count 
    FROM "work_experiences" 
    WHERE "userId" = p_user_id AND "verificationStatus" = 'APPROVED';
    
    -- 计算文件验证分数
    SELECT COUNT(*) INTO verified_files_count
    FROM "experience_files" ef
    JOIN "work_experiences" we ON ef."workExperienceId" = we."id"
    WHERE we."userId" = p_user_id AND ef."verificationStatus" = 'APPROVED';
    
    -- 计算薪资贡献分数
    SELECT COUNT(*) INTO verified_salaries_count
    FROM "salaries"
    WHERE "authorId" = p_user_id AND "isVerified" = true;
    
    -- 计算面经贡献分数
    SELECT COUNT(*) INTO verified_interviews_count
    FROM "interviews"
    WHERE "authorId" = p_user_id;
    
    -- 计算各维度分数 (简单算法，可以根据需要调整)
    work_exp_score := LEAST(verified_exp_count * 0.2, 1.0);
    salary_contrib_score := LEAST(verified_salaries_count * 0.15, 1.0);
    interview_contrib_score := LEAST(verified_interviews_count * 0.1, 1.0);
    
    -- 计算综合分数
    overall_score := (work_exp_score + salary_contrib_score + interview_contrib_score) / 3.0;
    
    -- 插入或更新用户信誉记录
    INSERT INTO "user_credibility" (
        "userId", 
        "overallScore", 
        "workExperienceScore", 
        "salaryContributionScore", 
        "interviewContributionScore",
        "verifiedExperiencesCount",
        "verifiedFilesCount",
        "verifiedSalariesCount",
        "verifiedInterviewsCount",
        "lastCalculatedAt"
    ) VALUES (
        p_user_id,
        overall_score,
        work_exp_score,
        salary_contrib_score,
        interview_contrib_score,
        verified_exp_count,
        verified_files_count,
        verified_salaries_count,
        verified_interviews_count,
        CURRENT_TIMESTAMP
    )
    ON CONFLICT ("userId") DO UPDATE SET
        "overallScore" = overall_score,
        "workExperienceScore" = work_exp_score,
        "salaryContributionScore" = salary_contrib_score,
        "interviewContributionScore" = interview_contrib_score,
        "verifiedExperiencesCount" = verified_exp_count,
        "verifiedFilesCount" = verified_files_count,
        "verifiedSalariesCount" = verified_salaries_count,
        "verifiedInterviewsCount" = verified_interviews_count,
        "updatedAt" = CURRENT_TIMESTAMP,
        "lastCalculatedAt" = CURRENT_TIMESTAMP;
END;
$$ LANGUAGE plpgsql;

-- 自动更新用户信誉的触发器函数
CREATE OR REPLACE FUNCTION trigger_update_user_credibility()
RETURNS TRIGGER AS $$
DECLARE
    affected_user_id UUID;
BEGIN
    -- 确定受影响的用户ID
    IF TG_TABLE_NAME = 'work_experiences' THEN
        affected_user_id := COALESCE(NEW."userId", OLD."userId");
    ELSIF TG_TABLE_NAME = 'salaries' THEN
        affected_user_id := COALESCE(NEW."authorId", OLD."authorId");
    ELSIF TG_TABLE_NAME = 'interviews' THEN
        affected_user_id := COALESCE(NEW."authorId", OLD."authorId");
    END IF;
    
    -- 重新计算用户信誉
    IF affected_user_id IS NOT NULL THEN
        PERFORM calculate_user_credibility(affected_user_id);
    END IF;
    
    RETURN COALESCE(NEW, OLD);
END;
$$ LANGUAGE plpgsql;

-- 创建触发器
CREATE TRIGGER trigger_work_experiences_credibility_update
    AFTER INSERT OR UPDATE OR DELETE ON "work_experiences"
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_credibility();

CREATE TRIGGER trigger_salaries_credibility_update
    AFTER INSERT OR UPDATE OR DELETE ON "salaries"
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_credibility();

CREATE TRIGGER trigger_interviews_credibility_update
    AFTER INSERT OR UPDATE OR DELETE ON "interviews"
    FOR EACH ROW EXECUTE FUNCTION trigger_update_user_credibility();

-- ================================
-- 工作经历相关视图
-- ================================

-- 用户工作经历汇总视图
CREATE OR REPLACE VIEW user_work_experience_summary AS
SELECT 
    u."id" as "userId",
    u."username",
    u."name",
    COUNT(we."id") as "totalExperiences",
    COUNT(CASE WHEN we."verificationStatus" = 'APPROVED' THEN 1 END) as "verifiedExperiences",
    COUNT(ef."id") as "totalFiles",
    COUNT(CASE WHEN ef."verificationStatus" = 'APPROVED' THEN 1 END) as "verifiedFiles",
    uc."overallScore",
    uc."workExperienceScore"
FROM "users" u
LEFT JOIN "work_experiences" we ON u."id" = we."userId"
LEFT JOIN "experience_files" ef ON we."id" = ef."workExperienceId"
LEFT JOIN "user_credibility" uc ON u."id" = uc."userId"
WHERE u."isActive" = true
GROUP BY u."id", u."username", u."name", uc."overallScore", uc."workExperienceScore";

-- 待审核内容视图

CREATE OR REPLACE VIEW pending_verifications AS
SELECT 
    'WORK_EXPERIENCE' as "contentType",
    we."id" as "contentId",
    we."companyName" as "title",
    we."position" as "subtitle",
    u."name" as "authorName",
    we."createdAt"
FROM "work_experiences" we
JOIN "users" u ON we."userId" = u."id"
WHERE we."verificationStatus" = 'PENDING'

UNION ALL

SELECT 
    'EXPERIENCE_FILE' as "contentType",
    ef."id" as "contentId",
    ef."title" as "title",
    ef."fileName" as "subtitle",
    u."name" as "authorName",
    ef."uploadedAt" as "createdAt"
FROM "experience_files" ef
JOIN "work_experiences" we ON ef."workExperienceId" = we."id"
JOIN "users" u ON we."userId" = u."id"
WHERE ef."verificationStatus" = 'PENDING'

ORDER BY "createdAt" DESC;

COMMIT;