-- WorkMates 数据库种子数据脚本
-- 插入初始测试数据

BEGIN;

-- ================================
-- 清理现有数据 (仅开发环境使用)
-- ================================

-- 注意：生产环境请谨慎使用，建议注释掉这部分
-- TRUNCATE TABLE reports, ratings, interviews, salaries, bookmarks, likes, comments, posts, companies, sessions, accounts, users RESTART IDENTITY CASCADE;

-- ================================
-- 插入测试用户数据
-- ================================

INSERT INTO
    users (
        id,
        email,
        username,
        name,
        password,
        position,
        company,
        experience,
        industry,
        level,
        points,
        reputation,
        is_verified,
        is_active,
        created_at
    )
VALUES
    -- 管理员用户
    (
        '550e8400-e29b-41d4-a716-************',
        '<EMAIL>',
        'admin',
        '系统管理员',
        '$2b$10$N9qo8uLOickgx2ZMRZoMye.J8.A.h.2N3Q2LIzkEu/f9U6ExPT.4.', -- password: admin123
        '系统管理员',
        'WorkMates',
        5,
        'IT',
        'ADMIN',
        10000,
        100.0,
        true,
        true,
        NOW() - INTERVAL '1 year'
    ),

-- 普通用户
(
    '550e8400-e29b-41d4-a716-************',
    '<EMAIL>',
    'zhangsan',
    '张三',
    '$2b$10$N9qo8uLOickgx2ZMRZoMye.J8.A.h.2N3Q2LIzkEu/f9U6ExPT.4.', -- password: 123456
    '前端开发工程师',
    '阿里巴巴',
    3,
    'IT',
    'ACTIVE',
    1500,
    4.5,
    true,
    true,
    NOW() - INTERVAL '6 months'
),
(
    '550e8400-e29b-41d4-a716-446655440003',
    '<EMAIL>',
    'lisi',
    '李四',
    '$2b$10$N9qo8uLOickgx2ZMRZoMye.J8.A.h.2N3Q2LIzkEu/f9U6ExPT.4.', -- password: 123456
    '后端开发工程师',
    '腾讯',
    5,
    'IT',
    'SENIOR',
    2800,
    4.8,
    true,
    true,
    NOW() - INTERVAL '8 months'
),
(
    '550e8400-e29b-41d4-a716-446655440004',
    '<EMAIL>',
    'wangwu',
    '王五',
    '$2b$10$N9qo8uLOickgx2ZMRZoMye.J8.A.h.2N3Q2LIzkEu/f9U6ExPT.4.', -- password: 123456
    '产品经理',
    '字节跳动',
    4,
    'IT',
    'ACTIVE',
    2200,
    4.2,
    true,
    true,
    NOW() - INTERVAL '4 months'
),
(
    '550e8400-e29b-41d4-a716-446655440005',
    '<EMAIL>',
    'zhaoliu',
    '赵六',
    '$2b$10$N9qo8uLOickgx2ZMRZoMye.J8.A.h.2N3Q2LIzkEu/f9U6ExPT.4.', -- password: 123456
    'UI设计师',
    '美团',
    2,
    'IT',
    'ACTIVE',
    800,
    3.8,
    false,
    true,
    NOW() - INTERVAL '2 months'
),
(
    '550e8400-e29b-41d4-a716-446655440006',
    '<EMAIL>',
    'chenqi',
    '陈七',
    '$2b$10$N9qo8uLOickgx2ZMRZoMye.J8.A.h.2N3Q2LIzkEu/f9U6ExPT.4.', -- password: 123456
    '数据分析师',
    '小米',
    3,
    'IT',
    'ACTIVE',
    1200,
    4.0,
    false,
    true,
    NOW() - INTERVAL '3 months'
);

-- ================================
-- 插入企业数据
-- ================================

INSERT INTO
    companies (
        id,
        name,
        name_en,
        description,
        website,
        industry,
        size,
        founded_year,
        headquarters,
        is_verified,
        is_active,
        total_ratings,
        average_rating,
        created_at
    )
VALUES (
        '660e8400-e29b-41d4-a716-************',
        '阿里巴巴集团',
        'Alibaba Group',
        '阿里巴巴集团控股有限公司是一家中国多元化科技公司，专注于电子商务、云计算、数字媒体和娱乐、创新举措等业务。',
        'https://www.alibaba.com',
        'IT',
        'ENTERPRISE',
        1999,
        '杭州',
        true,
        true,
        125,
        4.2,
        NOW() - INTERVAL '2 years'
    ),
    (
        '660e8400-e29b-41d4-a716-************',
        '腾讯控股有限公司',
        'Tencent Holdings Limited',
        '腾讯控股有限公司是一家中国的投资控股公司，其子公司主要从事互联网相关服务和产品、娱乐、人工智能和其他技术。',
        'https://www.tencent.com',
        'IT',
        'ENTERPRISE',
        1998,
        '深圳',
        true,
        true,
        98,
        4.1,
        NOW() - INTERVAL '2 years'
    ),
    (
        '660e8400-e29b-41d4-a716-446655440003',
        '字节跳动有限公司',
        'ByteDance Ltd.',
        '字节跳动是一家全球化的互联网技术公司，致力于用技术丰富人们的生活。',
        'https://www.bytedance.com',
        'IT',
        'ENTERPRISE',
        2012,
        '北京',
        true,
        true,
        110,
        4.3,
        NOW() - INTERVAL '1 year'
    ),
    (
        '660e8400-e29b-41d4-a716-446655440004',
        '美团',
        'Meituan',
        '美团是中国领先的生活服务电子商务平台，致力于帮助消费者发现最好的生活服务，帮助商家获得更多客户。',
        'https://www.meituan.com',
        'IT',
        'LARGE',
        2010,
        '北京',
        true,
        true,
        75,
        3.9,
        NOW() - INTERVAL '1 year'
    ),
    (
        '660e8400-e29b-41d4-a716-446655440005',
        '小米集团',
        'Xiaomi Corporation',
        '小米集团是一家专注于智能硬件和电子产品研发的移动互联网公司，同时也是一家专注于高端智能手机、互联网电视以及智能家居生态链建设的创新型科技企业。',
        'https://www.mi.com',
        'IT',
        'LARGE',
        2010,
        '北京',
        true,
        true,
        88,
        4.0,
        NOW() - INTERVAL '1 year'
    ),
    (
        '660e8400-e29b-41d4-a716-446655440006',
        '华为技术有限公司',
        'Huawei Technologies Co., Ltd.',
        '华为是全球领先的信息与通信技术(ICT)解决方案供应商，专注于ICT领域，坚持稳健经营、持续创新、开放合作。',
        'https://www.huawei.com',
        'IT',
        'ENTERPRISE',
        1987,
        '深圳',
        true,
        true,
        156,
        4.4,
        NOW() - INTERVAL '3 years'
    );

-- ================================
-- 插入帖子数据
-- ================================


INSERT INTO posts (
  id, title, content, excerpt, type, category, tags, company_id, author_id, 
  is_published, view_count, like_count, comment_count, created_at, published_at
) VALUES 
('770e8400-e29b-41d4-a716-************', '阿里巴巴前端面试经验分享', 
 '刚刚结束了阿里巴巴前端工程师的面试，想和大家分享一下经验。\n\n## 面试流程\n1. 一面：基础技术面试\n2. 二面：项目经验和算法\n3. 三面：系统设计和价值观\n\n## 技术问题\n主要考察了React、JavaScript基础、CSS布局、网络协议等知识点。\n\n## 建议\n1. 扎实的基础很重要\n2. 项目经验要能深入讲解\n3. 保持学习新技术的热情', 
 '刚刚结束了阿里巴巴前端工程师的面试，想和大家分享一下经验。', 
 'SHARING', '面试经验', ARRAY['前端', '阿里巴巴', '面试'], 
 '660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 
 true, 158, 23, 8, NOW() - INTERVAL '3 days', NOW() - INTERVAL '3 days'),

('770e8400-e29b-41d4-a716-************', '腾讯的工作环境怎么样？', 
 '最近在考虑跳槽到腾讯，想了解一下腾讯的工作环境和企业文化。\n\n有在腾讯工作过的朋友可以分享一下吗？主要想了解：\n1. 工作强度如何\n2. 团队氛围\n3. 晋升机制\n4. 福利待遇\n\n谢谢大家！', 
 '最近在考虑跳槽到腾讯，想了解一下腾讯的工作环境和企业文化。', 
 'QUESTION', '企业咨询', ARRAY['腾讯', '工作环境'], 
 '660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440003', 
 true, 89, 12, 15, NOW() - INTERVAL '5 days', NOW() - INTERVAL '5 days'),

('770e8400-e29b-41d4-a716-446655440003', '字节跳动2024年薪资水平分析', 
 '整理了一下字节跳动2024年各个级别的薪资数据，供大家参考。\n\n## 技术岗位\n- 初级工程师(1-1)：15-25k\n- 中级工程师(1-2)：25-35k\n- 高级工程师(2-1)：35-50k\n- 资深工程师(2-2)：50-70k\n\n## 产品岗位\n- 产品经理：20-40k\n- 高级产品经理：40-60k\n\n*以上数据来源于公开渠道和朋友分享，仅供参考*', 
 '整理了一下字节跳动2024年各个级别的薪资数据，供大家参考。', 
 'SHARING', '薪资分析', ARRAY['字节跳动', '薪资', '2024'], 
 '660e8400-e29b-41d4-a716-446655440003', '550e8400-e29b-41d4-a716-446655440004', 
 true, 245, 45, 22, NOW() - INTERVAL '1 week', NOW() - INTERVAL '1 week'),

('770e8400-e29b-41d4-a716-446655440004', '美团外卖产品经理日常工作分享', 
 '在美团做了2年产品经理，想和大家分享一下日常工作内容。\n\n## 主要职责\n1. 需求分析和产品规划\n2. 用户调研和数据分析\n3. 产品设计和原型制作\n4. 跨部门协调沟通\n\n## 工作感受\n1. 工作节奏比较快\n2. 数据驱动决策\n3. 用户至上的理念\n4. 学习成长机会很多\n\n## 建议\n想做产品经理的同学要多关注用户体验，培养数据敏感度。', 
 '在美团做了2年产品经理，想和大家分享一下日常工作内容。', 
 'SHARING', '职场经验', ARRAY['美团', '产品经理', '工作经验'], 
 '660e8400-e29b-41d4-a716-446655440004', '550e8400-e29b-41d4-a716-446655440004', 
 true, 134, 18, 11, NOW() - INTERVAL '4 days', NOW() - INTERVAL '4 days');

-- ================================
-- 插入评论数据
-- ================================

INSERT INTO
    comments (
        id,
        content,
        post_id,
        author_id,
        created_at
    )
VALUES (
        '880e8400-e29b-41d4-a716-************',
        '很有用的分享！我也在准备阿里的面试，请问算法题难度如何？',
        '770e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-446655440005',
        NOW() - INTERVAL '2 days'
    ),
    (
        '880e8400-e29b-41d4-a716-************',
        '感谢分享！我在腾讯工作3年了，整体来说工作氛围还是不错的，加班不算太严重。',
        '770e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-446655440003',
        NOW() - INTERVAL '4 days'
    ),
    (
        '880e8400-e29b-41d4-a716-446655440003',
        '数据很有参考价值！我朋友在字节，确实薪资水平不错。',
        '770e8400-e29b-41d4-a716-446655440003',
        '550e8400-e29b-41d4-a716-************',
        NOW() - INTERVAL '6 days'
    ),
    (
        '880e8400-e29b-41d4-a716-446655440004',
        '作为美团的同事，确实如此！产品经理的工作很有挑战性。',
        '770e8400-e29b-41d4-a716-446655440004',
        '550e8400-e29b-41d4-a716-446655440005',
        NOW() - INTERVAL '3 days'
    );

-- ================================
-- 插入薪资数据
-- ================================

INSERT INTO
    salaries (
        id,
        position,
        level,
        experience,
        base_salary,
        total_salary,
        bonus,
        work_location,
        work_type,
        company_id,
        author_id,
        is_verified,
        created_at
    )
VALUES (
        '990e8400-e29b-41d4-a716-************',
        '前端开发工程师',
        'P6',
        3,
        18000,
        280000,
        50000,
        '杭州',
        'FULL_TIME',
        '660e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-************',
        true,
        NOW() - INTERVAL '1 month'
    ),
    (
        '990e8400-e29b-41d4-a716-************',
        '后端开发工程师',
        'T3-1',
        5,
        32000,
        450000,
        80000,
        '深圳',
        'FULL_TIME',
        '660e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-446655440003',
        true,
        NOW() - INTERVAL '2 months'
    ),
    (
        '990e8400-e29b-41d4-a716-446655440003',
        '产品经理',
        '2-1',
        4,
        28000,
        380000,
        60000,
        '北京',
        'FULL_TIME',
        '660e8400-e29b-41d4-a716-446655440003',
        '550e8400-e29b-41d4-a716-446655440004',
        false,
        NOW() - INTERVAL '3 weeks'
    ),
    (
        '990e8400-e29b-41d4-a716-446655440004',
        'UI设计师',
        'P5',
        2,
        15000,
        200000,
        30000,
        '北京',
        'FULL_TIME',
        '660e8400-e29b-41d4-a716-446655440004',
        '550e8400-e29b-41d4-a716-446655440005',
        false,
        NOW() - INTERVAL '2 weeks'
    ),
    (
        '990e8400-e29b-41d4-a716-446655440005',
        '数据分析师',
        'P6',
        3,
        20000,
        260000,
        40000,
        '北京',
        'FULL_TIME',
        '660e8400-e29b-41d4-a716-446655440005',
        '550e8400-e29b-41d4-a716-446655440006',
        false,
        NOW() - INTERVAL '1 week'
    );

-- ================================
-- 插入面经数据
-- ================================


INSERT INTO interviews (
  id, position, department, level, process_steps, duration, difficulty, 
  questions, experience, tips, result, offer, company_id, author_id, interview_date, created_at
) VALUES 
('aa0e8400-e29b-41d4-a716-************', '前端开发工程师', '技术部', 'P6', 
 ARRAY['简历筛选', '一面技术', '二面综合', '三面HR'], 180, 'MEDIUM',
 ARRAY['React原理', 'JavaScript闭包', 'CSS布局', '项目难点'], 
 '整体面试体验不错，面试官很专业，会根据简历深入提问。技术面试主要考察基础知识和项目经验。',
 '建议提前准备项目中的技术难点，能够深入讲解实现思路。',
 'PASSED', true, '660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-************', 
 NOW() - INTERVAL '1 month', NOW() - INTERVAL '3 weeks'),

('aa0e8400-e29b-41d4-a716-************', '后端开发工程师', '技术部', 'T3-1', 
 ARRAY['笔试', '一面技术', '二面架构', '三面总监'], 240, 'HARD',
 ARRAY['MySQL优化', '分布式系统', 'Redis缓存', '系统设计'], 
 '面试难度较高，特别是系统设计环节。面试官会深入询问技术细节和解决方案。',
 '需要有扎实的技术基础，特别是分布式系统和数据库优化方面的知识。',
 'PASSED', true, '660e8400-e29b-41d4-a716-************', '550e8400-e29b-41d4-a716-446655440003', 
 NOW() - INTERVAL '2 months', NOW() - INTERVAL '7 weeks');

-- ================================
-- 插入企业评分数据
-- ================================

INSERT INTO
    ratings (
        id,
        overall,
        salary,
        environment,
        management,
        development,
        worklife,
        title,
        pros,
        cons,
        advice,
        company_id,
        author_id,
        created_at
    )
VALUES (
        'bb0e8400-e29b-41d4-a716-************',
        4.2,
        4.5,
        4.0,
        3.8,
        4.3,
        3.9,
        '阿里巴巴整体工作体验',
        '1. 薪资福利不错 2. 学习成长机会多 3. 技术氛围浓厚 4. 同事素质较高',
        '1. 工作压力比较大 2. 晋升竞争激烈 3. 部分团队加班较多',
        '适合想快速成长的年轻人，但要做好心理准备面对高强度工作。',
        '660e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-************',
        NOW() - INTERVAL '2 months'
    ),
    (
        'bb0e8400-e29b-41d4-a716-************',
        4.1,
        4.3,
        4.2,
        4.0,
        4.0,
        4.1,
        '腾讯工作体验分享',
        '1. 公司文化比较好 2. 工作生活平衡不错 3. 福利待遇完善 4. 办公环境舒适',
        '1. 部分业务发展较慢 2. 内部竞争激烈 3. 创新能力有待提升',
        '适合追求稳定发展的同学，公司氛围相对轻松。',
        '660e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-446655440003',
        NOW() - INTERVAL '1 month'
    );

-- ================================
-- 插入点赞数据
-- ================================

INSERT INTO
    likes (
        id,
        user_id,
        post_id,
        created_at
    )
VALUES (
        'cc0e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-446655440003',
        '770e8400-e29b-41d4-a716-************',
        NOW() - INTERVAL '2 days'
    ),
    (
        'cc0e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-446655440004',
        '770e8400-e29b-41d4-a716-************',
        NOW() - INTERVAL '2 days'
    ),
    (
        'cc0e8400-e29b-41d4-a716-446655440003',
        '550e8400-e29b-41d4-a716-446655440005',
        '770e8400-e29b-41d4-a716-446655440003',
        NOW() - INTERVAL '5 days'
    ),
    (
        'cc0e8400-e29b-41d4-a716-446655440004',
        '550e8400-e29b-41d4-a716-************',
        '770e8400-e29b-41d4-a716-446655440004',
        NOW() - INTERVAL '3 days'
    );

-- ================================
-- 插入收藏数据
-- ================================

INSERT INTO
    bookmarks (
        id,
        user_id,
        post_id,
        created_at
    )
VALUES (
        'dd0e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-446655440005',
        '770e8400-e29b-41d4-a716-************',
        NOW() - INTERVAL '2 days'
    ),
    (
        'dd0e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-446655440006',
        '770e8400-e29b-41d4-a716-446655440003',
        NOW() - INTERVAL '4 days'
    );

-- ================================
-- 更新统计计数
-- ================================

-- 更新帖子的评论数和点赞数
UPDATE posts
SET
    comment_count = (
        SELECT COUNT(*)
        FROM comments
        WHERE
            post_id = posts.id
    ),
    like_count = (
        SELECT COUNT(*)
        FROM likes
        WHERE
            post_id = posts.id
    );

-- 更新企业的评分和统计数据
UPDATE companies
SET
    total_ratings = (
        SELECT COUNT(*)
        FROM ratings
        WHERE
            company_id = companies.id
    ),
    average_rating = (
        SELECT COALESCE(AVG(overall), 0)
        FROM ratings
        WHERE
            company_id = companies.id
    ),
    total_salaries = (
        SELECT COUNT(*)
        FROM salaries
        WHERE
            company_id = companies.id
    );

-- 更新评论的点赞数
UPDATE comments
SET
    like_count = (
        SELECT COUNT(*)
        FROM likes
        WHERE
            comment_id = comments.id
    );

COMMIT;

-- ================================
-- 验证数据插入
-- ================================

-- 检查插入的数据量
SELECT 'Users' as table_name, COUNT(*) as count
FROM users
UNION ALL
SELECT 'Companies', COUNT(*)
FROM companies
UNION ALL
SELECT 'Posts', COUNT(*)
FROM posts
UNION ALL
SELECT 'Comments', COUNT(*)
FROM comments
UNION ALL
SELECT 'Salaries', COUNT(*)
FROM salaries
UNION ALL
SELECT 'Interviews', COUNT(*)
FROM interviews
UNION ALL
SELECT 'Ratings', COUNT(*)
FROM ratings
UNION ALL
SELECT 'Likes', COUNT(*)
FROM likes
UNION ALL
SELECT 'Bookmarks', COUNT(*)
FROM bookmarks;

-- ================================
-- 插入工作经历数据
-- ================================


INSERT INTO work_experiences (
  id, user_id, company_name, position, department, employment_type, 
  start_date, end_date, is_current, description, responsibilities, achievements, skills_used,
  salary_range, supervisor_name, verification_status, verification_score, created_at
) VALUES 
('cc0e8400-e29b-41d4-a716-446655440011', '550e8400-e29b-41d4-a716-************', 
 '阿里巴巴集团', '前端开发工程师', '技术部', 'FULL_TIME', 
 '2021-06-01', NULL, true, 
 '负责淘宝前端业务开发，参与多个重要项目的技术架构设计和实现。',
 ARRAY['前端架构设计', '业务需求开发', '性能优化', '团队协作'],
 ARRAY['优化页面加载速度50%', '设计可复用组件库', '带领3人小团队'],
 ARRAY['React', 'TypeScript', 'Node.js', 'Webpack'],
 '25k-30k', '李总监', 'APPROVED', 0.95, NOW() - INTERVAL '6 months'),

('cc0e8400-e29b-41d4-a716-446655440012', '550e8400-e29b-41d4-a716-446655440003', 
 '腾讯控股有限公司', '后端开发工程师', '微信事业群', 'FULL_TIME', 
 '2019-03-01', NULL, true, 
 '负责微信支付后端服务开发，处理高并发交易请求。',
 ARRAY['后端API开发', '数据库设计', '性能优化', '系统监控'],
 ARRAY['支持日均千万级交易', '系统可用性99.99%', '优化查询性能10倍'],
 ARRAY['Java', 'Spring Boot', 'MySQL', 'Redis', 'Kafka'],
 '35k-40k', '王架构师', 'APPROVED', 0.98, NOW() - INTERVAL '8 months'),

('cc0e8400-e29b-41d4-a716-446655440013', '550e8400-e29b-41d4-a716-446655440004', 
 '字节跳动有限公司', '产品经理', '抖音事业部', 'FULL_TIME', 
 '2020-09-01', NULL, true, 
 '负责抖音直播功能产品设计，推动用户体验优化。',
 ARRAY['产品需求分析', '用户体验设计', '数据分析', '项目管理'],
 ARRAY['直播DAU增长30%', '设计3个核心功能', '用户满意度提升20%'],
 ARRAY['Axure', 'Figma', 'SQL', 'Python'],
 '30k-35k', '张产品总监', 'PENDING', 0.85, NOW() - INTERVAL '4 months'),

('cc0e8400-e29b-41d4-a716-446655440014', '550e8400-e29b-41d4-a716-446655440005', 
 '美团', 'UI设计师', '设计中心', 'FULL_TIME', 
 '2022-08-01', NULL, true, 
 '负责美团外卖App界面设计，提升用户体验。',
 ARRAY['界面设计', '交互设计', '用户研究', '设计规范'],
 ARRAY['重新设计订单页面', '建立设计系统', '用户转化率提升15%'],
 ARRAY['Sketch', 'Figma', 'Principle', 'After Effects'],
 '18k-22k', '陈设计总监', 'APPROVED', 0.90, NOW() - INTERVAL '2 months'),

('cc0e8400-e29b-41d4-a716-446655440015', '550e8400-e29b-41d4-a716-446655440006', 
 '小米集团', '数据分析师', '数据部', 'FULL_TIME', 
 '2021-10-01', NULL, true, 
 '负责小米商城用户行为分析，为业务决策提供数据支持。',
 ARRAY['数据分析', '报表制作', '业务洞察', '模型搭建'],
 ARRAY['建立用户画像系统', '优化推荐算法效果', '业务增长20%'],
 ARRAY['Python', 'SQL', 'Tableau', 'R'],
 '22k-26k', '刘数据总监', 'PENDING', 0.88, NOW() - INTERVAL '3 months');

-- ================================
-- 插入工作经历文件数据
-- ================================

INSERT INTO
    experience_files (
        id,
        work_experience_id,
        file_name,
        file_original_name,
        file_path,
        file_size,
        file_type,
        file_category,
        title,
        description,
        verification_status,
        is_public,
        uploaded_at
    )
VALUES (
        'dd0e8400-e29b-41d4-a716-446655440011',
        'cc0e8400-e29b-41d4-a716-446655440011',
        'alibaba_contract_001.pdf',
        '阿里巴巴劳动合同.pdf',
        '/uploads/work_exp/dd0e8400_contract.pdf',
        2048000,
        'application/pdf',
        'CONTRACT',
        '阿里巴巴劳动合同',
        '入职时签署的正式劳动合同',
        'APPROVED',
        false,
        NOW() - INTERVAL '6 months'
    ),
    (
        'dd0e8400-e29b-41d4-a716-446655440012',
        'cc0e8400-e29b-41d4-a716-446655440012',
        'tencent_certificate_001.jpg',
        '腾讯优秀员工证书.jpg',
        '/uploads/work_exp/dd0e8400_cert.jpg',
        1536000,
        'image/jpeg',
        'CERTIFICATE',
        '腾讯优秀员工证书',
        '2023年度优秀员工奖励证书',
        'APPROVED',
        true,
        NOW() - INTERVAL '7 months'
    ),
    (
        'dd0e8400-e29b-41d4-a716-446655440013',
        'cc0e8400-e29b-41d4-a716-446655440013',
        'bytedance_photo_001.jpg',
        '字节跳动工作照.jpg',
        '/uploads/work_exp/dd0e8400_photo.jpg',
        1024000,
        'image/jpeg',
        'PHOTO',
        '字节跳动办公场所',
        '在字节跳动办公室的工作照片',
        'PENDING',
        false,
        NOW() - INTERVAL '4 months'
    );

-- ================================
-- 插入用户信誉数据
-- ================================

INSERT INTO
    user_credibility (
        id,
        user_id,
        overall_score,
        work_experience_score,
        salary_contribution_score,
        interview_contribution_score,
        verified_experiences_count,
        verified_files_count,
        verified_salaries_count,
        verified_interviews_count,
        total_contributions_count,
        helpful_contributions_count,
        flagged_contributions_count,
        created_at
    )
VALUES (
        'ee0e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-************',
        0.95,
        1.0,
        0.90,
        0.95,
        0,
        0,
        0,
        0,
        25,
        23,
        0,
        NOW() - INTERVAL '1 year'
    ),
    (
        'ee0e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-************',
        0.88,
        0.95,
        0.85,
        0.85,
        1,
        1,
        1,
        1,
        8,
        7,
        0,
        NOW() - INTERVAL '6 months'
    ),
    (
        'ee0e8400-e29b-41d4-a716-446655440003',
        '550e8400-e29b-41d4-a716-446655440003',
        0.92,
        0.98,
        0.90,
        0.88,
        1,
        1,
        1,
        1,
        12,
        11,
        0,
        NOW() - INTERVAL '8 months'
    ),
    (
        'ee0e8400-e29b-41d4-a716-446655440004',
        '550e8400-e29b-41d4-a716-446655440004',
        0.78,
        0.85,
        0.75,
        0.75,
        1,
        0,
        1,
        0,
        6,
        5,
        1,
        NOW() - INTERVAL '4 months'
    ),
    (
        'ee0e8400-e29b-41d4-a716-446655440005',
        '550e8400-e29b-41d4-a716-446655440005',
        0.72,
        0.90,
        0.60,
        0.65,
        1,
        0,
        1,
        0,
        4,
        3,
        0,
        NOW() - INTERVAL '2 months'
    ),
    (
        'ee0e8400-e29b-41d4-a716-446655440006',
        '550e8400-e29b-41d4-a716-446655440006',
        0.80,
        0.88,
        0.75,
        0.78,
        1,
        0,
        1,
        0,
        7,
        6,
        0,
        NOW() - INTERVAL '3 months'
    );

-- ================================
-- 插入审核记录数据
-- ================================

INSERT INTO
    verification_records (
        id,
        target_type,
        target_id,
        reviewer_id,
        previous_status,
        new_status,
        decision,
        confidence_level,
        review_notes,
        created_at
    )
VALUES (
        'ff0e8400-e29b-41d4-a716-************',
        'WORK_EXPERIENCE',
        'cc0e8400-e29b-41d4-a716-446655440011',
        '550e8400-e29b-41d4-a716-************',
        'PENDING',
        'APPROVED',
        'APPROVED',
        0.95,
        '工作经历信息详实，提供的证明材料充分，审核通过。',
        NOW() - INTERVAL '5 months'
    ),
    (
        'ff0e8400-e29b-41d4-a716-************',
        'EXPERIENCE_FILE',
        'dd0e8400-e29b-41d4-a716-446655440011',
        '550e8400-e29b-41d4-a716-************',
        'PENDING',
        'APPROVED',
        'APPROVED',
        0.98,
        '劳动合同文件清晰完整，信息真实有效，审核通过。',
        NOW() - INTERVAL '5 months'
    ),
    (
        'ff0e8400-e29b-41d4-a716-446655440003',
        'WORK_EXPERIENCE',
        'cc0e8400-e29b-41d4-a716-446655440012',
        '550e8400-e29b-41d4-a716-************',
        'PENDING',
        'APPROVED',
        'APPROVED',
        0.97,
        '腾讯工作经历验证通过，背景信息属实。',
        NOW() - INTERVAL '7 months'
    ),
    (
        'ff0e8400-e29b-41d4-a716-446655440004',
        'SALARY',
        '990e8400-e29b-41d4-a716-************',
        '550e8400-e29b-41d4-a716-************',
        'PENDING',
        'APPROVED',
        'APPROVED',
        0.92,
        '薪资信息与市场水平相符，审核通过。',
        NOW() - INTERVAL '4 weeks'
    );

-- 显示种子数据插入完成信息
SELECT '数据库种子数据插入完成！' as status;