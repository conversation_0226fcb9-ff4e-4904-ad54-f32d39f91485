# React 组件错误处理指南

## 概述

本文档详细说明了在 Next.js App Router 中常见的 React 组件错误类型及其解决方案，特别是 Server Component 和 Client Component 的区别和使用场景。

## 常见错误类型

### 1. Event handlers cannot be passed to Client Component props

#### 错误表现

```
Error: Event handlers cannot be passed to Client Component props.
<... variant="outline" size="lg" onClick={function onClick} children=...>
                                           ^^^^^^^^^^^^^^^^^^
If you need interactivity, consider converting part of this to a Client Component.
```

#### 错误原因

- 组件是 **Server Component**（默认）
- 但使用了客户端事件处理器（`onClick`, `onChange` 等）
- Server Components 在服务器端渲染，无法处理浏览器事件

#### 解决方案

在组件文件顶部添加 `'use client'` 指令：

```typescript
'use client'

import { Button } from '@/components/ui/button'
// 其他导入...

export default function MyComponent() {
  const handleClick = () => {
    console.log('Button clicked!')
  }

  return (
    <Button onClick={handleClick}>
      点击我
    </Button>
  )
}
```

## Server Component vs Client Component

### Server Component (默认)

**特点：**

- 在服务器端渲染
- 可以直接访问数据库和 API
- 包体积更小，首屏加载更快
- 不能使用浏览器 API
- 不能使用事件处理器
- 不能使用 React Hooks（useState, useEffect 等）

**适用场景：**

- 静态内容展示
- 数据获取和展示
- SEO 优化页面
- 不需要交互的组件

**示例：**

```typescript
// app/company/[id]/page.tsx - Server Component
export default async function CompanyPage({ params }: { params: { id: string } }) {
  // 可以直接在服务器端获取数据
  const company = await fetch(`/api/companies/${params.id}`)

  return (
    <div>
      <h1>{company.name}</h1>
      <p>{company.description}</p>
    </div>
  )
}
```

### Client Component

**特点：**

- 在客户端（浏览器）渲染
- 可以使用事件处理器
- 可以使用 React Hooks
- 可以访问浏览器 API
- 包体积相对较大

**适用场景：**

- 需要用户交互的组件
- 使用 React Hooks 的组件
- 需要访问浏览器 API 的组件
- 表单处理
- 状态管理

**示例：**

```typescript
'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'

export default function InteractiveComponent() {
  const [count, setCount] = useState(0)

  const handleClick = () => {
    setCount(prev => prev + 1)
  }

  return (
    <div>
      <p>点击次数: {count}</p>
      <Button onClick={handleClick}>点击 +1</Button>
    </div>
  )
}
```

## 需要添加 'use client' 的组件类型

### 1. 包含事件处理器的组件

```typescript
'use client'

function MyButton() {
  return (
    <button onClick={() => alert('Hello!')}>
      点击我
    </button>
  )
}
```

### 2. 使用 React Hooks 的组件

```typescript
'use client'

import { useState, useEffect } from 'react'

function CounterComponent() {
  const [count, setCount] = useState(0)

  useEffect(() => {
    console.log('Count changed:', count)
  }, [count])

  return <div>Count: {count}</div>
}
```

### 3. 需要访问浏览器 API 的组件

```typescript
'use client'

function LocationComponent() {
  const [location, setLocation] = useState('')

  useEffect(() => {
    setLocation(window.location.href)
  }, [])

  return <div>当前地址: {location}</div>
}
```

### 4. 使用第三方客户端库的组件

```typescript
'use client'

import { useSession } from 'next-auth/react'

function AuthComponent() {
  const { data: session } = useSession()

  return session ? <div>已登录</div> : <div>未登录</div>
}
```

## 最佳实践

### 1. 最小化 Client Components

- 只在必要时使用 `'use client'`
- 将交互逻辑封装在小的 Client Components 中
- 主页面结构尽量保持为 Server Component

### 2. 组件拆分策略

```typescript
// app/company/[id]/page.tsx - Server Component
export default async function CompanyPage({ params }: { params: { id: string } }) {
  const company = await getCompany(params.id)

  return (
    <div>
      <h1>{company.name}</h1>
      <CompanyInfo company={company} />
      <InteractiveSection companyId={company.id} /> {/* Client Component */}
    </div>
  )
}

// components/InteractiveSection.tsx - Client Component
'use client'

function InteractiveSection({ companyId }: { companyId: string }) {
  const [liked, setLiked] = useState(false)

  return (
    <button onClick={() => setLiked(!liked)}>
      {liked ? '已收藏' : '收藏'}
    </button>
  )
}
```

### 3. 错误预防

1. **明确组件职责**：数据展示用 Server Component，交互用 Client Component
2. **类型检查**：使用 TypeScript 帮助发现类型错误
3. **测试驱动**：编写测试确保组件按预期工作

## 常见问题排查

### 1. 组件无法使用 useState

**症状：** `useState is not defined` 或类似错误
**解决：** 添加 `'use client'` 指令

### 2. onClick 事件不工作

**症状：** 点击按钮没有反应
**解决：** 检查是否添加了 `'use client'` 指令

### 3. 浏览器 API 无法访问

**症状：** `window is not defined` 错误
**解决：**

- 添加 `'use client'` 指令
- 或在 `useEffect` 中使用浏览器 API

```typescript
'use client'

import { useEffect, useState } from 'react'

function MyComponent() {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
  }, [])

  if (!isClient) return null

  return <div>{window.location.href}</div>
}
```

## 总结

理解 Server Component 和 Client Component 的区别是使用 Next.js App Router 的关键。记住：

- **Server Component**：静态内容、数据获取、SEO 优化
- **Client Component**：用户交互、状态管理、浏览器 API

合理使用这两种组件类型可以获得最佳的性能和用户体验。
