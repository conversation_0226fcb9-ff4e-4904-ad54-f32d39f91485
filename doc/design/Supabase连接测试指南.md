# Supabase连接测试指南

## 概述
本文档提供了测试WorkMates项目与Supabase数据库连接的详细步骤和排查方法。

## 环境变量检查

### 1. 检查 .env.local 文件
确保以下环境变量正确配置：

```env
# Supabase数据库配置
DATABASE_URL="postgresql://postgres.zfctpeukxaxftfsmpqgp:<EMAIL>:6543/postgres"
DIRECT_URL="postgresql://postgres.zfctpeukxaxftfsmpqgp:<EMAIL>:6543/postgres"

# Supabase API配置
NEXT_PUBLIC_SUPABASE_URL="https://zfctpeukxaxftfsmpqgp.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpmY3RwZXVreGF4ZnRmc21wcWdwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3NjUyMTYsImV4cCI6MjA2NzM0MTIxNn0.CuPOCJ-RkxXNQb3A7yeTuS0WLtXQ6tF7gkUUEkO3Dx0"
```

## 连接测试脚本

### 2. 创建简单的连接测试脚本

在项目根目录创建 `test-db-connection.js` 文件：

```javascript
// test-db-connection.js
const { PrismaClient } = require('@prisma/client')
require('dotenv').config({ path: '.env.local' })

const prisma = new PrismaClient({
  log: ['query', 'info', 'warn', 'error'],
})

async function testConnection() {
  console.log('开始测试数据库连接...')
  console.log('DATABASE_URL:', process.env.DATABASE_URL ? '已设置' : '未设置')
  console.log('DIRECT_URL:', process.env.DIRECT_URL ? '已设置' : '未设置')
  
  try {
    // 测试基本连接
    console.log('\n1. 测试基本连接...')
    await prisma.$connect()
    console.log('✅ 数据库连接成功')
    
    // 测试查询
    console.log('\n2. 测试简单查询...')
    const result = await prisma.$queryRaw`SELECT 1 as test`
    console.log('✅ 查询测试成功:', result)
    
    // 测试表是否存在
    console.log('\n3. 检查表结构...')
    const tables = await prisma.$queryRaw`
      SELECT table_name 
      FROM information_schema.tables 
      WHERE table_schema = 'public'
      ORDER BY table_name
    `
    console.log('✅ 数据库表列表:')
    tables.forEach(table => console.log(`  - ${table.table_name}`))
    
    // 测试用户表
    console.log('\n4. 测试用户表...')
    const userCount = await prisma.user.count()
    console.log(`✅ 用户表记录数: ${userCount}`)
    
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message)
    console.error('错误详情:', error)
  } finally {
    await prisma.$disconnect()
    console.log('\n数据库连接已关闭')
  }
}

testConnection()
```

### 3. 运行连接测试

```bash
node test-db-connection.js
```

## Supabase客户端测试

### 4. 创建Supabase客户端测试脚本

创建 `test-supabase-client.js` 文件：

```javascript
// test-supabase-client.js
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

async function testSupabaseClient() {
  console.log('开始测试Supabase客户端连接...')
  console.log('Supabase URL:', supabaseUrl ? '已设置' : '未设置')
  console.log('Supabase Key:', supabaseKey ? '已设置' : '未设置')
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Supabase配置不完整')
    return
  }
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey)
    
    // 测试连接
    console.log('\n1. 测试Supabase连接...')
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)
    
    if (error) {
      console.error('❌ Supabase查询失败:', error.message)
    } else {
      console.log('✅ Supabase连接成功')
    }
    
    // 测试表列表
    console.log('\n2. 获取表信息...')
    const { data: tables, error: tablesError } = await supabase
      .rpc('get_table_list')
      .select()
    
    if (tablesError) {
      console.log('⚠️ 无法获取表列表 (这是正常的，如果没有自定义函数)')
    } else {
      console.log('✅ 表列表:', tables)
    }
    
  } catch (error) {
    console.error('❌ Supabase客户端测试失败:', error.message)
  }
}

testSupabaseClient()
```

### 5. 运行Supabase客户端测试

```bash
node test-supabase-client.js
```

## 常见问题排查

### 问题1: "prepared statement already exists"
**原因**: 连接池问题或并发连接冲突
**解决方案**:
1. 重启开发服务器
2. 清理Prisma缓存: `npx prisma generate`
3. 检查是否有多个Prisma实例运行

### 问题2: "WSAStartup failed"
**原因**: Windows网络初始化问题
**解决方案**:
1. 重启命令行工具
2. 检查网络连接
3. 尝试使用管理员权限运行

### 问题3: 连接超时
**原因**: 网络问题或Supabase服务问题
**解决方案**:
1. 检查网络连接
2. 验证Supabase项目状态
3. 检查防火墙设置

### 问题4: 认证失败
**原因**: 数据库密码或URL错误
**解决方案**:
1. 重新获取数据库连接字符串
2. 检查密码是否包含特殊字符需要编码
3. 验证项目ID和区域设置

## 手动验证步骤

### 6. 在Supabase Dashboard中验证

1. 登录 [Supabase Dashboard](https://app.supabase.com)
2. 选择项目 `zfctpeukxaxftfsmpqgp`
3. 检查项目状态是否为"Active"
4. 在SQL Editor中运行测试查询：
   ```sql
   SELECT current_database(), current_user, version();
   ```

### 7. 检查表结构

在SQL Editor中运行：
```sql
SELECT table_name, table_type 
FROM information_schema.tables 
WHERE table_schema = 'public' 
ORDER BY table_name;
```

### 8. 测试基本CRUD操作

```sql
-- 测试插入
INSERT INTO users (email, name) VALUES ('<EMAIL>', 'Test User');

-- 测试查询
SELECT id, email, name, created_at FROM users LIMIT 5;

-- 测试更新
UPDATE users SET name = 'Updated Test User' WHERE email = '<EMAIL>';

-- 测试删除
DELETE FROM users WHERE email = '<EMAIL>';
```

## 网络诊断

### 9. 测试网络连接

```bash
# 测试DNS解析
nslookup aws-0-ap-northeast-1.pooler.supabase.com

# 测试端口连接
telnet aws-0-ap-northeast-1.pooler.supabase.com 6543

# 或使用PowerShell (Windows)
Test-NetConnection -ComputerName aws-0-ap-northeast-1.pooler.supabase.com -Port 6543
```

## 下一步操作

请按照以下顺序执行测试：

1. **运行连接测试脚本**: `node test-db-connection.js`
2. **运行Supabase客户端测试**: `node test-supabase-client.js`
3. **检查Supabase Dashboard**中的项目状态
4. **运行网络诊断**命令
5. **将测试结果反馈**给开发团队

## 预期结果

正常情况下，您应该看到：
- ✅ 数据库连接成功
- ✅ 查询测试成功
- ✅ 表结构正常
- ✅ Supabase客户端连接成功

如果出现错误，请记录具体的错误信息和步骤，以便进一步排查。
