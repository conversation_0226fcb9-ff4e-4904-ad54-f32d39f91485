# WorkMates(职工圈) - 职场社区网站需求文档

## 项目概述

### 项目背景

WorkMates是一个专为打工人打造的职场信息分享与交流社区，旨在为求职者和在职人员提供真实、透明的企业信息和职场交流平台。

### 项目目标

- 构建一个可信的企业信息数据库
- 提供匿名安全的职场信息分享环境
- 打造活跃的职场交流社区
- 帮助用户做出更明智的职业决策

### 技术栈

- **前端框架**: Next.js 15
- **数据库**: PostgreSQL + Prisma ORM
- **认证**: NextAuth.js
- **UI框架**: Tailwind CSS + shadcn/ui
- **部署**: Vercel
- **搜索**: Elasticsearch (可选)

### 当前进度 (更新至2025年1月7日)

#### ✅ 已完成功能 (核心功能)

- ✅ 项目架构搭建 (100%)
- ✅ 基础UI组件 (100%)
- ✅ 页面静态开发 (100%) - 20+个核心页面全部完成
- ✅ 导航系统优化 (100%)
- ✅ 样式系统改进 (100%)
- ✅ 代码质量优化 (100%) - 所有ESLint错误已修复
- ✅ 错误处理页面 (100%) - 404页面和用户反馈页面
- ✅ 认证系统集成 (100%) - NextAuth.js v5 + Google OAuth完整集成
- ✅ 数据库连接 (100%) - Supabase + Prisma完全配置并测试通过
- ✅ 企业信息API (100%) - 列表、详情、CRUD全部完成
- ✅ 评价系统API (100%) - 字段验证通过，测试数据创建成功
- ✅ 薪资数据API (100%) - 字段验证通过，测试数据创建成功
- ✅ 面试经验API (100%) - 字段验证通过，测试数据创建成功

#### ⚠️ 待完成功能 (社区功能缺失)

- ❌ 论坛系统API (0%) - 帖子、评论、互动功能
- ❌ 用户资料管理 (30%) - 个人信息、工作经历、消息系统
- ❌ 搜索功能 (0%) - 全局搜索和高级搜索
- ❌ 内容审核管理 (0%) - 管理员系统、举报处理
- ❌ 高级功能 (0%) - 推荐系统、数据统计、通知系统

#### 📊 整体评估

**当前完成度**: 约**70%** (企业信息平台功能完整，但社区功能缺失)

---

## 核心功能模块

### 1. 企业信息查询系统

#### 1.1 企业档案

- **基本信息**
  - 企业名称、规模、行业分类
  - 总部地址、分公司分布
  - 企业官网、融资情况
  - 企业文化和价值观

- **评分体系**
  - 综合评分（1-5星）
  - 细分维度评分：
    - 薪资水平
    - 工作环境
    - 管理制度
    - 发展前景
    - 工作生活平衡

#### 1.2 薪资数据库

- **薪资信息展示**
  - 按职位、级别、地区分类
  - 基本工资、绩效奖金、股票期权
  - 年终奖、加班费等额外收入
  - 薪资涨幅历史数据

- **薪资数据提交**
  - 匿名提交机制
  - 数据验证和去重
  - 举报虚假信息功能

#### 1.3 面经分享

- **面试经验**
  - 面试流程详述
  - 面试题目收集
  - 面试官反馈
  - 面试结果统计

- **求职建议**
  - 简历优化建议
  - 面试技巧分享
  - 岗位要求解析

#### 1.4 企业风评

- **工作体验分享**
  - 日常工作内容
  - 团队氛围描述
  - 加班情况统计
  - 离职原因分析

- **福利待遇**
  - 五险一金详情
  - 带薪假期政策
  - 培训发展机会
  - 其他福利盘点

### 2. 社区论坛系统

#### 2.1 话题分类

- **行业讨论**
  - 互联网、金融、制造业等
  - 行业趋势分析
  - 政策影响讨论

- **职场话题**
  - 职业规划
  - 技能提升
  - 求职招聘
  - 职场人际关系

- **生活分享**
  - 工作生活平衡
  - 城市生活成本
  - 通勤交通讨论

#### 2.2 内容功能

- **发帖功能**
  - 富文本编辑器
  - 图片上传支持
  - 标签系统
  - 匿名发帖选项

- **互动功能**
  - 点赞、收藏、分享
  - 评论回复系统
  - @用户提醒
  - 内容举报功能

#### 2.3 内容推荐

- **个性化推荐**
  - 基于用户兴趣
  - 热门内容推荐
  - 关注用户动态

### 3. 用户系统

#### 3.1 账户管理

- **注册登录**
  - 邮箱注册
  - 第三方登录（微信、QQ）
  - 手机号验证

- **个人资料**
  - 基本信息设置
  - 职业背景
  - 隐私设置
  - 匿名模式切换

#### 3.2 用户权限

- **等级系统**
  - 新手、活跃用户、资深用户
  - 积分获取规则
  - 权限解锁机制

- **信誉体系**
  - 贡献度评估
  - 内容质量评分
  - 违规行为记录

#### 3.3 个人中心

- **我的内容**
  - 发布的帖子
  - 评论历史
  - 收藏夹管理

- **消息中心**
  - 系统通知
  - 私信功能
  - 关注动态

### 4. 数据管理系统

#### 4.1 内容审核

- **自动审核**
  - 敏感词过滤
  - 垃圾信息识别
  - 重复内容检测

- **人工审核**
  - 用户举报处理
  - 内容质量审核
  - 违规行为处罚

#### 4.2 数据质量

- **信息验证**
  - 薪资数据合理性检查
  - 企业信息真实性验证
  - 用户身份认证

---

## 技术架构设计

### 前端架构

```
src/
├── app/                    # App Router
│   ├── (auth)/            # 认证页面群组
│   ├── companies/         # 企业信息页面
│   ├── forum/             # 论坛页面
│   ├── profile/           # 用户中心
│   └── api/               # API路由
├── components/            # 可复用组件
│   ├── ui/                # 基础UI组件
│   ├── forms/             # 表单组件
│   └── layout/            # 布局组件
├── lib/                   # 工具库
│   ├── auth.ts            # 认证配置
│   ├── db.ts              # 数据库连接
│   └── utils.ts           # 工具函数
└── types/                 # TypeScript类型定义
```

### 数据库设计

- **用户表 (users)**
- **企业表 (companies)**
- **薪资表 (salaries)**
- **帖子表 (posts)**
- **评论表 (comments)**
- **面经表 (interviews)**
- **评分表 (ratings)**

### API设计

- RESTful API设计
- GraphQL查询支持（可选）
- 实时通信WebSocket

---

## 非功能性需求

### 1. 性能要求

- 页面加载时间 < 3秒
- 支持并发用户数 > 10,000
- 数据库查询响应时间 < 500ms

### 2. 安全要求

- 用户数据加密存储
- API接口安全认证
- 防XSS和SQL注入
- 隐私数据匿名化

### 3. 可用性要求

- 7×24小时服务可用性 > 99.5%
- 响应式设计适配移动端
- 支持主流浏览器

### 4. 扩展性要求

- 微服务架构支持
- 数据库分库分表
- CDN内容分发
- 缓存策略优化

---

## 开发计划 (更新至2025年1月7日)

### ✅ 第一阶段 (MVP) - 已完成

- [x] 基础页面搭建 (20+页面完成)
- [x] 用户注册登录 (NextAuth.js v5集成)
- [x] 企业基本信息展示 (完整API和前端)
- [ ] 简单论坛功能 ⚠️ **待完成**

### ✅ 第二阶段 (核心功能) - 大部分完成

- [x] 薪资数据系统 (API完成，字段验证通过)
- [x] 面经分享功能 (API完成，字段验证通过)
- [x] 评分评价系统 (API完成，字段验证通过)
- [ ] 内容搜索功能 ⚠️ **待开发**

### 🚧 第三阶段 (完善功能) - 待开始

- [ ] 用户权限系统 (基础认证已完成，需完善角色管理)
- [ ] 内容推荐算法
- [x] 移动端优化 (响应式设计已完成)
- [ ] 数据分析统计

### 🚧 第四阶段 (优化阶段) - 待开始

- [ ] 性能优化
- [ ] 安全加固
- [ ] 功能完善
- [ ] 上线部署

### 📋 当前优先级任务 (2025年1月)

**立即任务 (本周)**:

1. 论坛系统API开发 (帖子、评论)
2. 用户资料管理API
3. 前端页面连接到真实API

**短期任务 (2周内)**: 4. 搜索功能API 5. 消息系统API 6. 互动功能完善

**中期任务 (1个月内)**: 7. 管理系统API 8. 高级功能开发

---

## 风险评估

### 技术风险

- 数据量增长导致的性能问题
- 并发访问的系统稳定性
- 第三方服务依赖风险

### 业务风险

- 内容质量和真实性控制
- 用户隐私保护合规性
- 法律法规变化影响

### 运营风险

- 初期用户获取难度
- 内容生态建设挑战
- 竞争对手压力

---

## 成功指标

### 用户指标

- 注册用户数
- 日活跃用户数
- 用户留存率

### 内容指标

- 企业信息覆盖数量
- 用户生成内容数量
- 内容质量评分

### 业务指标

- 用户满意度
- 平台影响力
- 商业化潜力

---

## 后续发展方向

### 功能扩展

- 职位推荐系统
- 企业直招平台
- 职业技能培训
- 行业报告生成

### 商业化

- 企业认证服务
- 招聘广告投放
- 会员增值服务
- 数据洞察报告

---

_本文档将根据项目进展和用户反馈持续更新_
