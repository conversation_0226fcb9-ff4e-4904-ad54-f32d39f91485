# WorkMates 开发环境配置指南

## 📋 概述

本文档提供 WorkMates 项目的完整开发环境配置指南，包括环境要求、开发工具配置、调试设置和开发流程。

## 🛠️ 环境要求

### 基础要求
- **Node.js**: >= 18.17.0 (推荐使用 20.x LTS)
- **npm**: >= 9.0.0 或 **pnpm**: >= 8.0.0
- **Git**: 最新版本
- **VSCode**: 最新版本（推荐）

### 技术栈
- **前端**: Next.js 15 (App Router) + TypeScript + TailwindCSS
- **UI库**: shadcn/ui + Radix UI
- **数据库**: Supabase PostgreSQL
- **ORM**: Prisma
- **认证**: NextAuth.js
- **部署**: Vercel

## 🔧 开发工具配置

### VSCode 推荐扩展
```json
{
  "recommendations": [
    "esbenp.prettier-vscode",
    "dbaeumer.vscode-eslint",
    "bradlc.vscode-tailwindcss",
    "ms-vscode.vscode-typescript-next",
    "prisma.prisma",
    "dsznajder.es7-react-js-snippets",
    "usernamehw.errorlens",
    "eamodio.gitlens"
  ]
}
```

### VSCode 调试配置
创建 `.vscode/launch.json` 文件：

```json
{
  "version": "0.2.0",
  "configurations": [
    {
      "name": "🚀 Next.js: debug server-side",
      "type": "node",
      "request": "launch",
      "program": "${workspaceFolder}/node_modules/next/dist/bin/next",
      "args": ["dev"],
      "cwd": "${workspaceFolder}",
      "skipFiles": ["<node_internals>/**"],
      "env": {
        "NODE_OPTIONS": "--inspect"
      },
      "console": "integratedTerminal"
    },
    {
      "name": "🌐 Next.js: debug client-side",
      "type": "chrome",
      "request": "launch",
      "url": "http://localhost:3000",
      "webRoot": "${workspaceFolder}",
      "sourceMapPathOverrides": {
        "webpack:///./*": "${webRoot}/*"
      }
    }
  ]
}
```

## 🚀 快速开始

### 1. 项目初始化
```bash
# 克隆项目
git clone [repository-url]
cd WorkMates

# 安装依赖
npm install
# 或者使用 pnpm
pnpm install
```

### 2. 环境变量配置
复制 `env.example` 并创建 `.env.local`：

```bash
cp env.example .env.local
```

配置以下环境变量：
```env
# 数据库配置
DATABASE_URL="postgresql://..."
DIRECT_URL="postgresql://..."

# NextAuth.js 配置
NEXTAUTH_URL="http://localhost:3000"
NEXTAUTH_SECRET="your-secret-key"

# Supabase 配置
NEXT_PUBLIC_SUPABASE_URL="your-supabase-url"
NEXT_PUBLIC_SUPABASE_ANON_KEY="your-supabase-anon-key"
```

### 3. 数据库设置
```bash
# 生成 Prisma 客户端
npx prisma generate

# 同步数据库结构
npx prisma db push

# 查看数据库
npx prisma studio
```

### 4. 启动开发服务器
```bash
npm run dev
# 或者
pnpm dev
```

访问 http://localhost:3000 查看应用。

## 🎯 调试指南

### 服务端调试
1. 在代码中设置断点
2. 按 F5 或选择 "🚀 Next.js: debug server-side"
3. 访问相应的页面或 API 端点

### 客户端调试
1. 在 React 组件中设置断点
2. 选择 "🌐 Next.js: debug client-side"
3. 在浏览器中调试

### 数据库调试
```bash
# 查看数据库连接
npx prisma db pull

# 重置数据库
npx prisma migrate reset

# 查看数据库日志
npx prisma studio
```

## 🔍 常用开发命令

### 代码质量检查
```bash
# ESLint 检查
npm run lint

# TypeScript 类型检查
npm run type-check

# 代码格式化
npm run format
```

### 测试相关
```bash
# 运行数据库连接测试
node scripts/test-supabase.js

# 测试API接口
curl http://localhost:3000/api/test-db
```

### 构建和部署
```bash
# 构建生产版本
npm run build

# 启动生产服务器
npm start
```

## 🚨 故障排除指南

### 常见错误和解决方案

#### 1. Edge Runtime 错误：`process.on is not a function`

**错误表现：**
```
Error [TypeError]: process.on is not a function
at <unknown> (src/lib/prisma.ts:22)
```

**原因：** Next.js middleware 运行在 Edge Runtime 环境中，不支持 Node.js 的 `process` API。

**解决方案：** 在使用 `process.on` 前检查环境
```javascript
// 修改前（错误）
process.on('beforeExit', async () => {
  await prisma.$disconnect()
})

// 修改后（正确）
if (typeof process !== 'undefined' && process.on) {
  process.on('beforeExit', async () => {
    await prisma.$disconnect()
  })
}
```

#### 2. React 组件错误：`Event handlers cannot be passed to Client Component props`

**错误表现：**
```
Error: Event handlers cannot be passed to Client Component props.
<button onClick={function onClick} ...>
```

**原因：** React 组件缺少 `"use client"` 指令，在服务端渲染时不能处理客户端事件。

**解决方案：** 在需要处理事件的组件文件顶部添加 `"use client"`
```javascript
'use client'

import { Button } from '@/components/ui/button'
// 其他导入...
```

**需要添加 "use client" 的组件类型：**
- 包含事件处理器的组件（onClick, onChange 等）
- 使用 React Hooks 的组件（useState, useEffect 等）
- 需要访问浏览器 API 的组件

#### 3. 端口占用问题

**错误表现：**
```
⚠ Port 3000 is in use, using available port 3001 instead.
```

**解决方案：** 
- 检查其他运行的服务：`lsof -i :3000`
- 或者手动指定端口：`npm run dev -- -p 3002`

#### 4. Prisma 连接错误

**错误表现：** 数据库连接失败

**解决方案：**
1. 检查环境变量配置
2. 验证数据库连接：`npx prisma db pull`
3. 重新生成客户端：`npx prisma generate`

#### 5. TypeScript 类型错误

**解决方案：**
1. 重启 TypeScript 服务：`Ctrl+Shift+P` > "TypeScript: Restart TS Server"
2. 清理缓存：`rm -rf .next && npm run dev`

## 📝 开发规范

### 代码风格
- 使用 TypeScript 进行类型检查
- 遵循 ESLint 规则
- 使用 Prettier 进行代码格式化
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case

### 提交规范
```bash
# 提交格式
git commit -m "feat: 添加用户认证功能"
```

### React 组件开发规范
- 服务端组件默认不添加 "use client"
- 需要客户端交互的组件必须添加 "use client"
- 优先使用服务端组件，只在必要时使用客户端组件
- 将客户端逻辑尽可能下推到叶子组件

## 🔧 开发环境优化

### 性能优化
- 使用 `next/dynamic` 进行代码分割
- 合理使用 `memo`, `useMemo`, `useCallback`
- 避免在服务端组件中使用客户端 API

### 开发体验优化
- 配置 Fast Refresh
- 使用 TypeScript 严格模式
- 配置路径别名 `@/` 指向 `src/` 