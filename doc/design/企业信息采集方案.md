# WorkMates 企业信息采集方案

## 一、背景分析

### 1.1 现状分析
作为个人开发者运营的职场社区平台，WorkMates 需要建立一个全面、准确、实时更新的企业信息数据库。参考"看准网"、"职友集"、"领英"等成熟平台的做法，我们需要设计一个合法、高效、可持续的企业信息采集方案。

### 1.2 主要挑战
- **数据量大**：全国企业数量庞大，手动录入不现实
- **信息准确性**：需要验证信息的真实性和时效性
- **合法合规**：必须遵守相关法律法规，避免侵权
- **成本控制**：个人项目预算有限，需要低成本解决方案
- **技术复杂度**：需要自动化采集和处理能力

## 二、数据来源分析

### 2.1 公开数据源

#### 2.1.1 政府公开数据
- **国家企业信用信息公示系统**（https://www.gsxt.gov.cn）
  - 优点：权威、准确、合法
  - 数据：工商注册信息、年报数据、行政处罚等
  - 限制：有反爬虫机制，需要遵守使用规范

- **地方政府数据开放平台**
  - 各省市的数据开放平台（如上海数据、北京数据等）
  - 包含企业基本信息、纳税信息等
  - 通常提供API接口，便于调用

- **行业监管部门公开信息**
  - 证监会、银保监会等行业监管数据
  - 上市公司信息披露平台

#### 2.1.2 商业公开数据
- **企业官网**
  - 企业介绍、产品服务、联系方式
  - 招聘信息、企业文化
  - 新闻动态、发展历程

- **招聘平台公开数据**
  - BOSS直聘、智联招聘、前程无忧等平台的企业主页
  - 招聘职位、薪资范围、企业规模
  - 需要注意版权和使用条款

- **企业黄页和B2B平台**
  - 阿里巴巴、慧聪网等B2B平台
  - 企业联系方式、主营业务

#### 2.1.3 第三方数据服务
- **天眼查、企查查API**
  - 提供标准化的企业信息API
  - 付费服务，但数据质量高
  - 适合初期快速获取基础数据

- **百度企业信用API**
  - 基础信息查询服务
  - 有免费额度

### 2.2 用户贡献数据（UGC）
- **用户提交的企业信息**
  - 员工分享的内部信息
  - 面试者提供的面试信息
  - 离职员工的工作体验

- **众包数据收集**
  - 设计激励机制鼓励用户贡献
  - 建立数据贡献者等级体系

## 三、技术实现方案

### 3.1 数据采集架构

```mermaid
graph TD
    A[数据源] --> B[采集层]
    B --> C[清洗层]
    C --> D[验证层]
    D --> E[存储层]
    E --> F[应用层]
    
    B --> B1[爬虫服务]
    B --> B2[API调用]
    B --> B3[用户提交]
    
    C --> C1[数据清洗]
    C --> C2[格式标准化]
    C --> C3[去重处理]
    
    D --> D1[交叉验证]
    D --> D2[人工审核]
    D --> D3[可信度评分]
```

### 3.2 具体技术栈

#### 3.2.1 数据采集工具
```typescript
// 1. 爬虫框架选择
- Playwright/Puppeteer: 处理动态网页和反爬虫
- Scrapy + Splash: Python爬虫框架（如需要）
- Node.js原生爬虫: 简单页面采集

// 2. API集成
- 天眼查/企查查 SDK
- 政府开放数据API
- OAuth认证集成

// 3. 定时任务
- Node-cron: 定时采集任务
- Bull Queue: 任务队列管理
```

#### 3.2.2 数据处理流程
```typescript
// src/lib/crawler/company-crawler.ts
import { chromium } from 'playwright';
import { prisma } from '@/lib/db';
import { CompanySchema } from '@/lib/validations/company';

export class CompanyCrawler {
  // 采集企业基本信息
  async crawlCompanyInfo(companyName: string) {
    const browser = await chromium.launch();
    const page = await browser.newPage();
    
    try {
      // 1. 从多个源采集数据
      const gsxtData = await this.crawlGSXT(page, companyName);
      const tyccData = await this.crawlFromAPI(companyName);
      
      // 2. 数据合并和清洗
      const mergedData = this.mergeCompanyData(gsxtData, tyccData);
      
      // 3. 数据验证
      const validatedData = CompanySchema.parse(mergedData);
      
      // 4. 存储到数据库
      await this.saveToDatabase(validatedData);
      
    } finally {
      await browser.close();
    }
  }
  
  // 数据去重和合并
  private mergeCompanyData(...sources: any[]) {
    // 实现数据合并逻辑
    // 优先级：政府数据 > 付费API > 公开网页
  }
}
```

### 3.3 数据库设计优化

```sql
-- 企业信息主表
CREATE TABLE companies (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  unified_code VARCHAR(50) UNIQUE, -- 统一社会信用代码
  name VARCHAR(200) NOT NULL,
  name_en VARCHAR(200),
  short_name VARCHAR(100),
  
  -- 基本信息
  legal_person VARCHAR(50),
  registered_capital DECIMAL(15,2),
  establishment_date DATE,
  company_type VARCHAR(50),
  industry_category VARCHAR(100),
  business_scope TEXT,
  
  -- 联系信息
  province VARCHAR(50),
  city VARCHAR(50),
  district VARCHAR(50),
  address TEXT,
  phone VARCHAR(50),
  email VARCHAR(100),
  website VARCHAR(200),
  
  -- 规模信息
  employee_count_min INT,
  employee_count_max INT,
  
  -- 数据来源和可信度
  data_source JSONB, -- 记录各字段的数据来源
  credibility_score FLOAT DEFAULT 0, -- 可信度评分
  
  -- 审核状态
  verification_status VARCHAR(20) DEFAULT 'pending',
  verified_at TIMESTAMP,
  verified_by UUID,
  
  -- 时间戳
  created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  last_crawled_at TIMESTAMP
);

-- 数据来源记录表
CREATE TABLE data_sources (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id),
  source_type VARCHAR(50), -- 'gsxt', 'tycc', 'user', 'official_website'
  source_url TEXT,
  raw_data JSONB,
  crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 数据验证记录表
CREATE TABLE data_validations (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  company_id UUID REFERENCES companies(id),
  field_name VARCHAR(50),
  old_value TEXT,
  new_value TEXT,
  validation_method VARCHAR(50),
  confidence_level FLOAT,
  validated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);
```

## 四、数据验证策略

### 4.1 多源交叉验证
```typescript
// src/lib/validator/company-validator.ts
export class CompanyValidator {
  // 交叉验证企业信息
  async validateCompanyInfo(companyId: string) {
    const sources = await this.getDataSources(companyId);
    
    // 1. 核心字段一致性检查
    const coreFields = ['unified_code', 'legal_person', 'registered_capital'];
    const validationResults = this.crossValidateFields(sources, coreFields);
    
    // 2. 计算可信度分数
    const credibilityScore = this.calculateCredibilityScore(validationResults);
    
    // 3. 标记冲突数据
    await this.markConflicts(companyId, validationResults);
    
    return {
      isValid: credibilityScore > 0.7,
      score: credibilityScore,
      conflicts: validationResults.conflicts
    };
  }
  
  // 可信度评分算法
  private calculateCredibilityScore(validationResults: any) {
    let score = 0;
    
    // 基础分：数据源数量
    score += Math.min(validationResults.sourceCount * 0.1, 0.3);
    
    // 一致性分：字段匹配度
    score += validationResults.matchRate * 0.4;
    
    // 权威性分：政府数据源权重更高
    if (validationResults.hasGovSource) score += 0.2;
    
    // 时效性分：数据更新时间
    const daysSinceUpdate = validationResults.daysSinceLastUpdate;
    score += Math.max(0, 0.1 - daysSinceUpdate / 365 * 0.1);
    
    return score;
  }
}
```

### 4.2 人工审核机制
- **众包审核**：让高级用户参与数据审核
- **专家审核**：关键企业由专人审核
- **举报机制**：用户可以举报错误信息
- **定期抽查**：随机抽查数据准确性

### 4.3 数据更新策略
```typescript
// 更新优先级队列
export class UpdatePriorityQueue {
  // 计算更新优先级
  calculatePriority(company: Company) {
    let priority = 0;
    
    // 热门企业优先更新
    priority += company.viewCount * 0.3;
    
    // 长时间未更新的提高优先级
    const daysSinceUpdate = this.daysSince(company.lastCrawledAt);
    priority += Math.min(daysSinceUpdate / 30, 1) * 0.3;
    
    // 用户反馈多的优先更新
    priority += company.feedbackCount * 0.2;
    
    // 数据可信度低的优先更新
    priority += (1 - company.credibilityScore) * 0.2;
    
    return priority;
  }
}
```

## 五、合规性方案

### 5.1 法律合规要点
1. **数据来源合法性**
   - 只采集公开数据
   - 遵守robots.txt协议
   - 控制访问频率，避免对源网站造成压力

2. **个人信息保护**
   - 不采集个人隐私信息
   - 对涉及个人的信息进行脱敏处理
   - 遵守《个人信息保护法》

3. **知识产权保护**
   - 标注数据来源
   - 不直接复制受版权保护的内容
   - 数据二次加工，形成自有数据资产

### 5.2 技术合规措施
```typescript
// src/lib/crawler/compliance.ts
export class CrawlerCompliance {
  // 检查robots.txt
  async checkRobotsTxt(url: string): Promise<boolean> {
    const robotsUrl = new URL('/robots.txt', url).href;
    const rules = await this.parseRobotsTxt(robotsUrl);
    return this.isAllowed(rules, url);
  }
  
  // 访问频率控制
  async rateLimit(domain: string) {
    const key = `rate_limit:${domain}`;
    const count = await redis.incr(key);
    
    if (count === 1) {
      await redis.expire(key, 60); // 60秒过期
    }
    
    if (count > 10) { // 每分钟最多10次
      throw new Error('Rate limit exceeded');
    }
  }
  
  // 数据脱敏
  sanitizePersonalInfo(data: any) {
    // 手机号脱敏
    data.phone = data.phone?.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
    // 邮箱脱敏
    data.email = data.email?.replace(/(.{3}).*(@.*)/, '$1***$2');
    return data;
  }
}
```

## 六、成本效益分析

### 6.1 成本构成
1. **技术成本**
   - 服务器资源：约 ¥200-500/月
   - API服务费用：约 ¥500-2000/月（根据调用量）
   - 存储成本：约 ¥100-300/月

2. **时间成本**
   - 开发时间：2-3周
   - 维护时间：每周2-4小时

3. **风险成本**
   - 法律风险：通过合规措施降低
   - 数据质量风险：通过验证机制控制

### 6.2 效益分析
1. **直接效益**
   - 快速建立企业数据库（预计3个月内达到10万+企业）
   - 提升平台内容丰富度和用户体验
   - 降低运营成本（相比人工录入节省95%以上）

2. **间接效益**
   - 建立数据壁垒和竞争优势
   - 为后续商业化奠定基础
   - 积累数据处理和分析能力

## 七、实施计划

### 7.1 第一阶段（第1-2周）：基础建设
- [ ] 搭建爬虫基础框架
- [ ] 集成主要API服务
- [ ] 设计数据库结构
- [ ] 实现基础数据采集功能

### 7.2 第二阶段（第3-4周）：数据采集
- [ ] 采集种子企业数据（1000家热门企业）
- [ ] 实现多源数据合并
- [ ] 开发数据验证功能
- [ ] 建立更新机制

### 7.3 第三阶段（第5-6周）：优化完善
- [ ] 优化采集效率和准确性
- [ ] 建立人工审核流程
- [ ] 完善合规性措施
- [ ] 开发数据管理后台

### 7.4 第四阶段（持续）：运营维护
- [ ] 监控数据质量
- [ ] 处理用户反馈
- [ ] 持续扩充数据源
- [ ] 优化更新策略

## 八、技术实现示例

### 8.1 主采集服务
```typescript
// src/services/crawler/index.ts
import { CronJob } from 'cron';
import { CompanyCrawler } from './company-crawler';
import { DataValidator } from './data-validator';
import { UpdateQueue } from './update-queue';

export class CrawlerService {
  private crawler: CompanyCrawler;
  private validator: DataValidator;
  private queue: UpdateQueue;
  
  constructor() {
    this.crawler = new CompanyCrawler();
    this.validator = new DataValidator();
    this.queue = new UpdateQueue();
  }
  
  // 启动定时任务
  start() {
    // 每天凌晨2点执行全量更新检查
    new CronJob('0 2 * * *', async () => {
      await this.dailyUpdate();
    }).start();
    
    // 每小时执行增量更新
    new CronJob('0 * * * *', async () => {
      await this.hourlyUpdate();
    }).start();
    
    // 实时处理用户提交的数据
    this.processUserSubmissions();
  }
  
  // 日常更新任务
  private async dailyUpdate() {
    const companies = await this.queue.getHighPriorityCompanies(1000);
    
    for (const company of companies) {
      try {
        const newData = await this.crawler.crawlCompanyInfo(company.name);
        const validatedData = await this.validator.validate(newData);
        await this.updateCompanyData(company.id, validatedData);
      } catch (error) {
        console.error(`Failed to update company ${company.id}:`, error);
      }
    }
  }
}
```

### 8.2 API路由实现
```typescript
// src/app/api/companies/import/route.ts
import { NextRequest, NextResponse } from 'next/server';
import { getServerSession } from 'next-auth';
import { authOptions } from '@/lib/auth';
import { CompanyCrawler } from '@/services/crawler';

export async function POST(request: NextRequest) {
  const session = await getServerSession(authOptions);
  
  if (!session || session.user.role !== 'admin') {
    return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
  }
  
  const { companyName, source } = await request.json();
  
  try {
    const crawler = new CompanyCrawler();
    const data = await crawler.crawlCompanyInfo(companyName, source);
    
    return NextResponse.json({
      success: true,
      data
    });
  } catch (error) {
    return NextResponse.json({
      success: false,
      error: error.message
    }, { status: 500 });
  }
}
```

## 九、风险控制

### 9.1 技术风险
- **反爬虫应对**：使用代理池、模拟真实用户行为
- **数据不一致**：建立数据版本控制和回滚机制
- **性能瓶颈**：使用分布式架构和缓存优化

### 9.2 法律风险
- **定期法律审查**：每季度进行合规性评估
- **建立举报响应机制**：24小时内响应数据问题举报
- **数据使用协议**：明确告知用户数据来源和使用范围

## 十、总结

本方案通过结合多种数据源和技术手段，为 WorkMates 项目提供了一个可行的企业信息采集解决方案。关键成功因素包括：

1. **合法合规**：严格遵守相关法律法规
2. **技术可行**：采用成熟的技术栈和架构
3. **成本可控**：适合个人项目的预算范围
4. **持续优化**：建立反馈和改进机制

通过实施本方案，预计能在3-6个月内建立起一个包含10万+企业的基础数据库，为平台的发展奠定坚实基础。

---

## 附录：参考资源

### 技术文档
- [Playwright 文档](https://playwright.dev/)
- [Prisma 文档](https://www.prisma.io/docs)
- [Bull Queue 文档](https://docs.bullmq.io/)

### API服务
- [天眼查开放平台](https://open.tianyancha.com/)
- [企查查API](https://openapi.qcc.com/)
- [百度企业信用](https://xin.baidu.com/)

### 法律法规
- 《中华人民共和国数据安全法》
- 《中华人民共和国个人信息保护法》
- 《互联网信息服务管理办法》

### 行业最佳实践
- [Web Scraping Best Practices](https://www.scrapehero.com/web-scraping-best-practices/)
- [数据采集合规指南](https://www.law.com/data-compliance-guide)

---

*本文档会根据项目进展和法规变化持续更新* 