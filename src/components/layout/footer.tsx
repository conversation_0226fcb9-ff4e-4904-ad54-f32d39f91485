import Link from 'next/link'
import { Mail, Github } from 'lucide-react'

/**
 * 页脚组件
 * 包含网站的导航链接、联系信息和版权声明
 */
export function Footer() {
  const currentYear = new Date().getFullYear()

  // 页脚链接分组
  const footerLinks = {
    产品功能: [
      { name: '企业查询', href: '/companies' },
      { name: '薪资数据', href: '/companies/salaries' },
      { name: '面经分享', href: '/companies/interviews' },
      { name: '社区论坛', href: '/forum' },
    ],
    关于我们: [
      { name: '关于 WorkMates', href: '/about' },
      { name: '使用条款', href: '/terms' },
      { name: '隐私政策', href: '/privacy' },
      { name: '联系我们', href: '/contact' },
    ],
    资源: [
      { name: '帮助中心', href: '/help' },
      { name: '常见问题', href: '/faq' },
      { name: '社区准则', href: '/guidelines' },
      { name: '反馈建议', href: '/feedback' },
    ],
  }

  return (
    <footer className="bg-gray-50 border-t">
      <div className="container mx-auto px-4 py-12">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          {/* 品牌信息 */}
          <div className="space-y-4">
            <h3 className="text-lg font-semibold">WorkMates</h3>
            <p className="text-sm text-gray-600">
              专为打工人打造的职场信息分享与交流社区，提供真实透明的企业信息。
            </p>
            {/* 社交媒体链接 */}
            <div className="flex space-x-4">
              <a
                href="mailto:<EMAIL>"
                className="text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="邮箱联系"
              >
                <Mail className="h-5 w-5" />
              </a>
              <a
                href="https://github.com/workmates"
                className="text-gray-400 hover:text-gray-600 transition-colors"
                aria-label="GitHub"
              >
                <Github className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* 导航链接 */}
          {Object.entries(footerLinks).map(([category, links]) => (
            <div key={category}>
              <h3 className="text-sm font-semibold text-gray-900 mb-4">
                {category}
              </h3>
              <ul className="space-y-3">
                {links.map(link => (
                  <li key={link.href}>
                    <Link
                      href={link.href}
                      className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
                    >
                      {link.name}
                    </Link>
                  </li>
                ))}
              </ul>
            </div>
          ))}
        </div>

        {/* 版权信息 */}
        <div className="mt-12 pt-8 border-t border-gray-200">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <p className="text-sm text-gray-600">
              © {currentYear} WorkMates. 保留所有权利。
            </p>
            <div className="flex space-x-6">
              <Link
                href="/terms"
                className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                使用条款
              </Link>
              <Link
                href="/privacy"
                className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                隐私政策
              </Link>
              <Link
                href="/cookies"
                className="text-sm text-gray-600 hover:text-gray-900 transition-colors"
              >
                Cookie 政策
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  )
}
