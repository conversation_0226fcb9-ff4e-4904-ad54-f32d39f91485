import { Header } from './header'
import { Footer } from './footer'

/**
 * 主布局组件
 * 包含页头、主内容区域和页脚
 * @param children - 页面主要内容
 */
interface MainLayoutProps {
  children: React.ReactNode
}

export function MainLayout({ children }: MainLayoutProps) {
  return (
    <div className="min-h-screen flex flex-col">
      {/* 页头导航 */}
      <Header />

      {/* 主内容区域 - flex-1 确保内容区域占据剩余空间 */}
      <main className="flex-1">{children}</main>

      {/* 页脚 */}
      <Footer />
    </div>
  )
}
