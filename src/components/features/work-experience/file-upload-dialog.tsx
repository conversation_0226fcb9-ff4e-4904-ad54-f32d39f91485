'use client'

import { <PERSON><PERSON>, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    <PERSON><PERSON>,
    DialogContent,
    DialogHeader,
    DialogTitle,
} from '@/components/ui/dialog'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Progress } from '@/components/ui/progress'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
    ExperienceFile,
    FILE_CATEGORY_CONFIG,
    FileCategory,
    formatFileSize,
    getFileCategoryConfig,
    getVerificationStatusConfig,
    WorkExperience,
} from '@/types/work-experience'
import {
    AlertCircle,
    CheckCircle,
    Clock,
    Download,
    Eye,
    File,
    FileText,
    Image,
    Trash2,
    Upload,
    XCircle,
} from 'lucide-react'
import { useRef, useState } from 'react'

interface FileUploadDialogProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  experience: WorkExperience | null
  files: ExperienceFile[]
}

interface FileUploadItem {
  id: string
  file: File
  category: FileCategory
  title: string
  description: string
  progress: number
  status: 'pending' | 'uploading' | 'success' | 'error'
  error?: string
}

export function FileUploadDialog({
  open,
  onOpenChange,
  experience,
  files,
}: FileUploadDialogProps) {
  const [uploadFiles, setUploadFiles] = useState<FileUploadItem[]>([])
  const [selectedCategory, setSelectedCategory] =
    useState<FileCategory>('DOCUMENT')
  const fileInputRef = useRef<HTMLInputElement>(null)

  const handleFileSelect = (e: React.ChangeEvent<HTMLInputElement>) => {
    const selectedFiles = Array.from(e.target.files || [])

    selectedFiles.forEach(file => {
      // 文件大小检查（10MB限制）
      if (file.size > 10 * 1024 * 1024) {
        alert(`文件 ${file.name} 超过10MB限制`)
        return
      }

      // 文件类型检查
      const categoryConfig = getFileCategoryConfig(selectedCategory)
      if (
        !categoryConfig.acceptedTypes.includes('*') &&
        !categoryConfig.acceptedTypes.includes(file.type)
      ) {
        alert(`文件 ${file.name} 类型不支持`)
        return
      }

      const uploadItem: FileUploadItem = {
        id: Math.random().toString(36).substr(2, 9),
        file,
        category: selectedCategory,
        title: file.name.split('.')[0],
        description: '',
        progress: 0,
        status: 'pending',
      }

      setUploadFiles(prev => [...prev, uploadItem])
    })

    // 清空input
    if (fileInputRef.current) {
      fileInputRef.current.value = ''
    }
  }

  const updateUploadFile = (id: string, updates: Partial<FileUploadItem>) => {
    setUploadFiles(prev =>
      prev.map(item => (item.id === id ? { ...item, ...updates } : item))
    )
  }

  const removeUploadFile = (id: string) => {
    setUploadFiles(prev => prev.filter(item => item.id !== id))
  }

  const startUpload = async (item: FileUploadItem) => {
    updateUploadFile(item.id, { status: 'uploading', progress: 0 })

    try {
      // 模拟文件上传进度
      for (let i = 0; i <= 100; i += 10) {
        await new Promise(resolve => setTimeout(resolve, 100))
        updateUploadFile(item.id, { progress: i })
      }

      // TODO: 实际的文件上传API调用
      console.log('上传文件:', {
        file: item.file,
        category: item.category,
        title: item.title,
        description: item.description,
        workExperienceId: experience?.id,
      })

      updateUploadFile(item.id, {
        status: 'success',
        progress: 100,
      })
    } catch {
      updateUploadFile(item.id, {
        status: 'error',
        error: '上传失败，请重试',
      })
    }
  }

  const startUploadAll = async () => {
    const pendingFiles = uploadFiles.filter(item => item.status === 'pending')

    for (const item of pendingFiles) {
      await startUpload(item)
    }
  }

  const handleViewFile = (file: ExperienceFile) => {
    // TODO: 实现文件预览
    console.log('预览文件:', file)
  }

  const handleDownloadFile = (file: ExperienceFile) => {
    // TODO: 实现文件下载
    console.log('下载文件:', file)
  }

  const handleDeleteFile = async (fileId: string) => {
    if (confirm('确定要删除这个文件吗？')) {
      // TODO: 调用删除API
      console.log('删除文件:', fileId)
    }
  }

  const getFileIcon = (fileType: string) => {
    if (fileType.startsWith('image/')) {
      return Image
    } else if (
      fileType === 'application/pdf' ||
      fileType.includes('document')
    ) {
      return FileText
    } else {
      return File
    }
  }

  const getStatusIcon = (status: FileUploadItem['status']) => {
    switch (status) {
      case 'pending':
        return Clock
      case 'uploading':
        return Upload
      case 'success':
        return CheckCircle
      case 'error':
        return XCircle
      default:
        return File
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>
            文件管理 - {experience?.companyName} {experience?.position}
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {/* 文件上传区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Upload className="h-5 w-5" />
                上传文件
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  上传相关证明文件可以提高工作经历的审核通过率。支持的文件类型：PDF、图片、Word文档等。
                </AlertDescription>
              </Alert>

              <div className="grid gap-4 md:grid-cols-2">
                <div className="space-y-2">
                  <Label>文件类别</Label>
                  <Select
                    value={selectedCategory}
                    onValueChange={(value: FileCategory) =>
                      setSelectedCategory(value)
                    }
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="选择文件类别" />
                    </SelectTrigger>
                    <SelectContent>
                      {Object.values(FILE_CATEGORY_CONFIG).map(config => (
                        <SelectItem
                          key={config.category}
                          value={config.category}
                        >
                          {config.label} - {config.description}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label>选择文件</Label>
                  <Input
                    ref={fileInputRef}
                    type="file"
                    multiple
                    accept={getFileCategoryConfig(
                      selectedCategory
                    ).acceptedTypes.join(',')}
                    onChange={handleFileSelect}
                  />
                </div>
              </div>

              {/* 上传队列 */}
              {uploadFiles.length > 0 && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h4 className="font-medium">
                      上传队列 ({uploadFiles.length})
                    </h4>
                    <Button
                      onClick={startUploadAll}
                      disabled={uploadFiles.every(f => f.status !== 'pending')}
                      size="sm"
                    >
                      全部上传
                    </Button>
                  </div>

                  <div className="space-y-2">
                    {uploadFiles.map(item => {
                      const StatusIcon = getStatusIcon(item.status)
                      const FileIcon = getFileIcon(item.file.type)

                      return (
                        <Card key={item.id} className="p-4">
                          <div className="flex items-center justify-between">
                            <div className="flex items-center gap-3 flex-1">
                              <FileIcon className="h-8 w-8 text-gray-400" />
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center gap-2">
                                  <span className="font-medium truncate">
                                    {item.file.name}
                                  </span>
                                  <Badge variant="outline">
                                    {getFileCategoryConfig(item.category).label}
                                  </Badge>
                                  <StatusIcon
                                    className={`h-4 w-4 ${
                                      item.status === 'success'
                                        ? 'text-green-600'
                                        : item.status === 'error'
                                          ? 'text-red-600'
                                          : item.status === 'uploading'
                                            ? 'text-blue-600'
                                            : 'text-gray-400'
                                    }`}
                                  />
                                </div>
                                <div className="text-sm text-gray-500">
                                  {formatFileSize(item.file.size)}
                                  {item.status === 'uploading' &&
                                    ` • ${item.progress}%`}
                                  {item.status === 'error' &&
                                    ` • ${item.error}`}
                                </div>

                                {item.status === 'uploading' && (
                                  <Progress
                                    value={item.progress}
                                    className="mt-2"
                                  />
                                )}

                                {item.status === 'pending' && (
                                  <div className="mt-2 space-y-2">
                                    <Input
                                      placeholder="文件标题"
                                      value={item.title}
                                      onChange={e =>
                                        updateUploadFile(item.id, {
                                          title: e.target.value,
                                        })
                                      }
                                    />
                                    <Textarea
                                      placeholder="文件描述（可选）"
                                      value={item.description}
                                      onChange={(e: React.ChangeEvent<HTMLTextAreaElement>) =>
                                        updateUploadFile(item.id, {
                                          description: e.target.value,
                                        })
                                      }
                                      rows={2}
                                    />
                                  </div>
                                )}
                              </div>
                            </div>

                            <div className="flex gap-2">
                              {item.status === 'pending' && (
                                <Button
                                  size="sm"
                                  onClick={() => startUpload(item)}
                                >
                                  上传
                                </Button>
                              )}
                              {item.status === 'error' && (
                                <Button
                                  size="sm"
                                  variant="outline"
                                  onClick={() => startUpload(item)}
                                >
                                  重试
                                </Button>
                              )}
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => removeUploadFile(item.id)}
                              >
                                <Trash2 className="h-4 w-4" />
                              </Button>
                            </div>
                          </div>
                        </Card>
                      )
                    })}
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 已上传文件列表 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5" />
                已上传文件 ({files.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              {files.length > 0 ? (
                <div className="grid gap-4 md:grid-cols-2">
                  {files.map(file => {
                    const categoryConfig = getFileCategoryConfig(
                      file.fileCategory
                    )
                    const statusConfig = getVerificationStatusConfig(
                      file.verificationStatus
                    )
                    const FileIcon = getFileIcon(file.fileType)

                    const StatusIcon =
                      {
                        Clock: Clock,
                        CheckCircle: CheckCircle,
                        XCircle: XCircle,
                        Ban: XCircle,
                      }[statusConfig.icon] || Clock

                    return (
                      <Card key={file.id} className="p-4">
                        <div className="flex items-start justify-between">
                          <div className="flex items-start gap-3 flex-1">
                            <FileIcon className="h-8 w-8 text-gray-400 mt-1" />
                            <div className="flex-1 min-w-0">
                              <div
                                className="font-medium truncate"
                                title={file.fileOriginalName}
                              >
                                {file.title || file.fileOriginalName}
                              </div>
                              <div className="text-sm text-gray-500 mt-1">
                                {formatFileSize(file.fileSize)} •{' '}
                                {file.fileType.split('/')[1]?.toUpperCase()}
                              </div>
                              <div className="flex gap-2 mt-2">
                                <Badge variant="outline">
                                  {categoryConfig.label}
                                </Badge>
                                <Badge
                                  variant={
                                    statusConfig.color === 'green'
                                      ? 'default'
                                      : 'secondary'
                                  }
                                >
                                  <StatusIcon className="w-3 h-3 mr-1" />
                                  {statusConfig.label}
                                </Badge>
                              </div>
                              {file.description && (
                                <p className="text-xs text-gray-600 mt-2 line-clamp-2">
                                  {file.description}
                                </p>
                              )}
                              <div className="text-xs text-gray-500 mt-2">
                                上传于 {file.uploadedAt.toLocaleDateString()}
                              </div>
                            </div>
                          </div>

                          <div className="flex gap-1 ml-2">
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleViewFile(file)}
                              title="预览"
                            >
                              <Eye className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDownloadFile(file)}
                              title="下载"
                            >
                              <Download className="h-4 w-4" />
                            </Button>
                            <Button
                              variant="ghost"
                              size="sm"
                              onClick={() => handleDeleteFile(file.id)}
                              title="删除"
                              className="text-red-600 hover:text-red-700"
                            >
                              <Trash2 className="h-4 w-4" />
                            </Button>
                          </div>
                        </div>
                      </Card>
                    )
                  })}
                </div>
              ) : (
                <div className="text-center py-8 text-gray-500">
                  <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                  <p>还没有上传任何文件</p>
                  <p className="text-sm mt-1">
                    上传相关证明文件来提高审核通过率
                  </p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* 关闭按钮 */}
          <div className="flex justify-end">
            <Button onClick={() => onOpenChange(false)}>关闭</Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}
