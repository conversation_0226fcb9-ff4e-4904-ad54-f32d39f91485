'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
  EMPLOYMENT_TYPE_CONFIG,
  EmploymentType,
  WorkExperience,
  WorkExperienceFormData,
} from '@/types/work-experience'
import { zodResolver } from '@hookform/resolvers/zod'
import {
  AlertCircle,
  Building2,
  Calendar,
  CheckCircle,
  DollarSign,
  Minus,
  Plus,
} from 'lucide-react'
import { useEffect, useState } from 'react'
import { useFieldArray, useForm } from 'react-hook-form'
import * as z from 'zod'

// 表单验证schema
const workExperienceSchema = z
  .object({
    companyName: z
      .string()
      .min(1, '请输入公司名称')
      .max(200, '公司名称不能超过200字符'),
    position: z
      .string()
      .min(1, '请输入职位名称')
      .max(100, '职位名称不能超过100字符'),
    department: z.string().max(100, '部门名称不能超过100字符').optional(),
    employmentType: z.enum([
      'FULL_TIME',
      'PART_TIME',
      'CONTRACT',
      'INTERNSHIP',
    ]),
    startDate: z.date({ required_error: '请选择开始日期' }),
    endDate: z.date().optional(),
    isCurrent: z.boolean(),
    description: z.string().max(1000, '工作描述不能超过1000字符').optional(),
    responsibilities: z
      .array(z.string().max(200, '职责描述不能超过200字符'))
      .optional(),
    achievements: z
      .array(z.string().max(200, '成就描述不能超过200字符'))
      .optional(),
    skillsUsed: z
      .array(z.string().max(50, '技能名称不能超过50字符'))
      .optional(),
    salaryRange: z.string().max(50, '薪资范围不能超过50字符').optional(),
    supervisorName: z.string().max(100, '主管姓名不能超过100字符').optional(),
    supervisorContact: z
      .string()
      .max(100, '联系方式不能超过100字符')
      .optional(),
  })
  .refine(
    data => {
      if (!data.isCurrent && !data.endDate) {
        return false
      }
      if (data.endDate && data.startDate > data.endDate) {
        return false
      }
      return true
    },
    {
      message: '结束日期必须晚于开始日期，或勾选"目前在职"',
      path: ['endDate'],
    }
  )

type FormData = z.infer<typeof workExperienceSchema>

interface WorkExperienceFormProps {
  experience?: WorkExperience | null
  onSubmit: (data: WorkExperienceFormData) => void
  onCancel: () => void
}

export function WorkExperienceForm({
  experience,
  onSubmit,
  onCancel,
}: WorkExperienceFormProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [message, setMessage] = useState<{
    type: 'success' | 'error'
    text: string
  } | null>(null)

  const form = useForm<FormData>({
    resolver: zodResolver(workExperienceSchema),
    defaultValues: {
      companyName: experience?.companyName || '',
      position: experience?.position || '',
      department: experience?.department || '',
      employmentType: experience?.employmentType || 'FULL_TIME',
      startDate: experience?.startDate || new Date(),
      endDate: experience?.endDate,
      isCurrent: experience?.isCurrent || false,
      description: experience?.description || '',
      responsibilities: experience?.responsibilities || [],
      achievements: experience?.achievements || [],
      skillsUsed: experience?.skillsUsed || [],
      salaryRange: experience?.salaryRange || '',
      supervisorName: experience?.supervisorName || '',
      supervisorContact: experience?.supervisorContact || '',
    },
  })

  const isCurrent = form.watch('isCurrent')

  // 动态数组字段管理
  const {
    fields: responsibilityFields,
    append: addResponsibility,
    remove: removeResponsibility,
  } = useFieldArray({
    control: form.control,
    name: 'responsibilities',
  })

  const {
    fields: achievementFields,
    append: addAchievement,
    remove: removeAchievement,
  } = useFieldArray({
    control: form.control,
    name: 'achievements',
  })

  const {
    fields: skillFields,
    append: addSkill,
    remove: removeSkill,
  } = useFieldArray({
    control: form.control,
    name: 'skillsUsed',
  })

  // 当勾选"目前在职"时，清空结束日期
  useEffect(() => {
    if (isCurrent) {
      form.setValue('endDate', undefined)
    }
  }, [isCurrent, form])

  const handleSubmit = async (data: FormData) => {
    setIsLoading(true)
    setMessage(null)

    try {
      // 处理表单数据
      const formData: WorkExperienceFormData = {
        ...data,
        responsibilities: data.responsibilities?.filter(r => r.trim()) || [],
        achievements: data.achievements?.filter(a => a.trim()) || [],
        skillsUsed: data.skillsUsed?.filter(s => s.trim()) || [],
      }

      // TODO: 调用API保存数据
      console.log('保存工作经历:', formData)

      setTimeout(() => {
        setMessage({ type: 'success', text: '工作经历保存成功' })
        setIsLoading(false)
        onSubmit(formData)
      }, 1000)
    } catch {
      setMessage({ type: 'error', text: '保存失败，请稍后重试' })
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={form.handleSubmit(handleSubmit)} className="space-y-6">
      {/* 消息提示 */}
      {message && (
        <Alert variant={message.type === 'error' ? 'destructive' : 'default'}>
          {message.type === 'success' ? (
            <CheckCircle className="h-4 w-4" />
          ) : (
            <AlertCircle className="h-4 w-4" />
          )}
          <AlertDescription>{message.text}</AlertDescription>
        </Alert>
      )}

      {/* 基本信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Building2 className="h-5 w-5" />
            基本信息
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="companyName">公司名称 *</Label>
              <Input
                id="companyName"
                {...form.register('companyName')}
                placeholder="请输入公司名称"
              />
              {form.formState.errors.companyName && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.companyName.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="position">职位名称 *</Label>
              <Input
                id="position"
                {...form.register('position')}
                placeholder="请输入职位名称"
              />
              {form.formState.errors.position && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.position.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="department">所属部门</Label>
              <Input
                id="department"
                {...form.register('department')}
                placeholder="请输入所属部门"
              />
              {form.formState.errors.department && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.department.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label>工作类型</Label>
              <Select
                value={form.watch('employmentType')}
                onValueChange={(value: EmploymentType) =>
                  form.setValue('employmentType', value)
                }
              >
                <SelectTrigger>
                  <SelectValue placeholder="请选择工作类型" />
                </SelectTrigger>
                <SelectContent>
                  {Object.values(EMPLOYMENT_TYPE_CONFIG).map(config => (
                    <SelectItem key={config.type} value={config.type}>
                      {config.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 工作时间 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Calendar className="h-5 w-5" />
            工作时间
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="startDate">开始日期 *</Label>
              <Input
                id="startDate"
                type="date"
                {...form.register('startDate', { valueAsDate: true })}
              />
              {form.formState.errors.startDate && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.startDate.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="endDate">结束日期</Label>
              <Input
                id="endDate"
                type="date"
                disabled={isCurrent}
                {...form.register('endDate', { valueAsDate: true })}
              />
              {form.formState.errors.endDate && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.endDate.message}
                </p>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isCurrent"
              checked={isCurrent}
              onCheckedChange={checked =>
                form.setValue('isCurrent', checked as boolean)
              }
            />
            <Label htmlFor="isCurrent">目前在职</Label>
          </div>
        </CardContent>
      </Card>

      {/* 工作内容 */}
      <Card>
        <CardHeader>
          <CardTitle>工作内容</CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="description">工作描述</Label>
            <Textarea
              id="description"
              {...form.register('description')}
              placeholder="简要描述你的工作内容和主要职责"
              rows={3}
            />
            {form.formState.errors.description && (
              <p className="text-sm text-red-600">
                {form.formState.errors.description.message}
              </p>
            )}
          </div>

          {/* 工作职责 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>工作职责</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addResponsibility('')}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {responsibilityFields.map((field, index) => (
              <div key={field.id} className="flex gap-2">
                <Input
                  {...form.register(`responsibilities.${index}` as const)}
                  placeholder={`职责 ${index + 1}`}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeResponsibility(index)}
                >
                  <Minus className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>

          {/* 主要成就 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>主要成就</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addAchievement('')}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {achievementFields.map((field, index) => (
              <div key={field.id} className="flex gap-2">
                <Input
                  {...form.register(`achievements.${index}`)}
                  placeholder={`成就 ${index + 1}`}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeAchievement(index)}
                >
                  <Minus className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>

          {/* 使用技能 */}
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <Label>使用技能</Label>
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={() => addSkill('')}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            {skillFields.map((field, index) => (
              <div key={field.id} className="flex gap-2">
                <Input
                  {...form.register(`skillsUsed.${index}`)}
                  placeholder={`技能 ${index + 1}`}
                />
                <Button
                  type="button"
                  variant="outline"
                  size="sm"
                  onClick={() => removeSkill(index)}
                >
                  <Minus className="h-4 w-4" />
                </Button>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* 其他信息 */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <DollarSign className="h-5 w-5" />
            其他信息（可选）
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div className="space-y-2">
              <Label htmlFor="salaryRange">薪资范围</Label>
              <Input
                id="salaryRange"
                {...form.register('salaryRange')}
                placeholder="如：15k-25k"
              />
              {form.formState.errors.salaryRange && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.salaryRange.message}
                </p>
              )}
            </div>

            <div className="space-y-2">
              <Label htmlFor="supervisorName">直属主管</Label>
              <Input
                id="supervisorName"
                {...form.register('supervisorName')}
                placeholder="直属主管姓名"
              />
              {form.formState.errors.supervisorName && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.supervisorName.message}
                </p>
              )}
            </div>

            <div className="space-y-2 md:col-span-2">
              <Label htmlFor="supervisorContact">主管联系方式</Label>
              <Input
                id="supervisorContact"
                {...form.register('supervisorContact')}
                placeholder="邮箱或电话（仅用于审核验证）"
              />
              {form.formState.errors.supervisorContact && (
                <p className="text-sm text-red-600">
                  {form.formState.errors.supervisorContact.message}
                </p>
              )}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 操作按钮 */}
      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={onCancel}>
          取消
        </Button>
        <Button type="submit" disabled={isLoading}>
          {isLoading ? '保存中...' : experience ? '更新' : '保存'}
        </Button>
      </div>
    </form>
  )
}
