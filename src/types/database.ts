// 基础的数据库类型定义
// 这个文件将在连接Supabase后通过CLI自动生成

export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: Json | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      // 主要表类型定义 - 基于Prisma schema生成
      users: {
        Row: {
          id: string
          email: string
          username?: string
          name?: string
          avatar?: string
          bio?: string
          level?: string
          points?: number
          reputation?: number
          isVerified?: boolean
          isActive?: boolean
          createdAt?: Date
          updatedAt?: Date
        }
      }
      companies: {
        Row: {
          id: string
          name: string
          nameEn?: string
          logo?: string
          description?: string
          website?: string
          industry?: string
          size?: string
          foundedYear?: number
          headquarters?: string
          isVerified?: boolean
          isActive?: boolean
          totalRatings?: number
          averageRating?: number
          totalSalaries?: number
          totalReviews?: number
          createdAt?: Date
          updatedAt?: Date
        }
      }
      posts: {
        Row: {
          id: string
          title: string
          content: string
          excerpt?: string
          type?: string
          category?: string
          tags: string[]
          companyId?: string
          authorId: string
          isAnonymous?: boolean
          isPublished?: boolean
          isPinned?: boolean
          isDeleted?: boolean
          viewCount?: number
          likeCount?: number
          commentCount?: number
          shareCount?: number
          createdAt?: Date
          updatedAt?: Date
          publishedAt?: Date
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
    CompositeTypes: {
      [_ in never]: never
    }
  }
}
