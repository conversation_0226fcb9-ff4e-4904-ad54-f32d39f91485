import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    AlertTriangle,
    Calendar,
    Database,
    Eye,
    Lock,
    Mail,
    Settings,
    Shield,
    Users
} from 'lucide-react'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '隐私政策 - WorkMates',
  description: '了解 WorkMates 如何收集、使用和保护您的个人信息'
}

// 隐私政策章节数据
const sections = [
  {
    id: 'information-collection',
    title: '信息收集',
    icon: <Database className="h-5 w-5" />,
    content: [
      {
        subtitle: '我们收集的信息类型',
        items: [
          '账户信息：用户名、邮箱地址、密码（加密存储）',
          '个人资料：头像、昵称、职业信息、工作经验',
          '使用数据：浏览记录、搜索历史、互动行为',
          '设备信息：IP地址、浏览器类型、操作系统',
          '位置信息：基于IP的大致地理位置（可选）'
        ]
      },
      {
        subtitle: '信息收集方式',
        items: [
          '您主动提供的信息：注册、填写个人资料、发布内容时',
          '自动收集的信息：通过Cookie、日志文件等技术手段',
          '第三方提供的信息：社交媒体登录、推荐功能',
          '公开信息：您在平台上公开发布的内容和评论'
        ]
      }
    ]
  },
  {
    id: 'information-use',
    title: '信息使用',
    icon: <Eye className="h-5 w-5" />,
    content: [
      {
        subtitle: '使用目的',
        items: [
          '提供和改进我们的服务功能',
          '个性化内容推荐和用户体验优化',
          '账户安全保护和欺诈检测',
          '客户服务和技术支持',
          '法律合规和争议解决',
          '产品开发和数据分析（匿名化处理）'
        ]
      },
      {
        subtitle: '数据处理原则',
        items: [
          '最小化原则：仅收集必要的信息',
          '目的限制：仅用于明确告知的目的',
          '数据准确性：确保信息的准确和及时更新',
          '存储限制：不超过必要的保存期限',
          '透明度：清楚告知数据处理活动'
        ]
      }
    ]
  },
  {
    id: 'information-sharing',
    title: '信息共享',
    icon: <Users className="h-5 w-5" />,
    content: [
      {
        subtitle: '我们不会出售您的个人信息',
        items: [
          '我们承诺不会向任何第三方出售您的个人数据',
          '不会将您的信息用于商业营销（除非您明确同意）',
          '严格限制内部员工对个人数据的访问权限'
        ]
      },
      {
        subtitle: '有限的信息共享情况',
        items: [
          '服务提供商：云存储、数据分析等必要的技术服务',
          '法律要求：响应合法的政府请求或法院命令',
          '安全保护：防止欺诈、恶意活动或保护用户安全',
          '业务转让：在合并、收购等情况下（会提前通知）'
        ]
      }
    ]
  },
  {
    id: 'data-security',
    title: '数据安全',
    icon: <Lock className="h-5 w-5" />,
    content: [
      {
        subtitle: '技术安全措施',
        items: [
          'HTTPS加密传输：所有数据传输采用SSL/TLS加密',
          '数据库加密：敏感信息在数据库中加密存储',
          '访问控制：严格的权限管理和身份认证机制',
          '安全审计：定期进行安全评估和漏洞扫描',
          '备份保护：数据备份采用加密和异地存储'
        ]
      },
      {
        subtitle: '管理安全措施',
        items: [
          '员工培训：定期进行数据安全和隐私保护培训',
          '访问日志：记录和监控所有数据访问活动',
          '事件响应：建立完善的安全事件响应机制',
          '合规审查：定期进行隐私合规性检查'
        ]
      }
    ]
  },
  {
    id: 'user-rights',
    title: '用户权利',
    icon: <Settings className="h-5 w-5" />,
    content: [
      {
        subtitle: '您的数据权利',
        items: [
          '访问权：您可以请求查看我们持有的您的个人信息',
          '更正权：您可以要求更正不准确或不完整的信息',
          '删除权：您可以要求删除您的个人信息',
          '限制处理权：在特定情况下限制对您数据的处理',
          '数据可携带权：以结构化格式获取您的数据',
          '反对权：反对基于合法利益的数据处理'
        ]
      },
      {
        subtitle: '如何行使权利',
        items: [
          '登录账户：在个人设置中直接管理您的信息',
          '联系我们：通过客服邮箱提交数据权利请求',
          '身份验证：为保护您的隐私，可能需要验证身份',
          '响应时间：我们会在30天内响应您的请求'
        ]
      }
    ]
  },
  {
    id: 'cookies',
    title: 'Cookie使用',
    icon: <Database className="h-5 w-5" />,
    content: [
      {
        subtitle: 'Cookie类型',
        items: [
          '必要Cookie：维持网站基本功能，无法禁用',
          '功能Cookie：记住您的偏好设置和登录状态',
          '分析Cookie：帮助我们了解网站使用情况',
          '广告Cookie：目前不使用，如有变化会提前通知'
        ]
      },
      {
        subtitle: 'Cookie管理',
        items: [
          '浏览器设置：您可以通过浏览器设置管理Cookie',
          '选择性禁用：除必要Cookie外，其他可以选择禁用',
          '影响说明：禁用某些Cookie可能影响网站功能'
        ]
      }
    ]
  }
]

export default function PrivacyPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面头部 */}
      <div className="text-center mb-12">
        <div className="flex justify-center mb-6">
          <div className="p-4 bg-primary/10 rounded-full">
            <Shield className="h-12 w-12 text-primary" />
          </div>
        </div>
        <h1 className="text-4xl font-bold mb-4">隐私政策</h1>
        <p className="text-xl text-muted-foreground mb-6 max-w-3xl mx-auto">
          WorkMates 深知隐私保护的重要性，我们致力于透明地说明如何收集、使用和保护您的个人信息。
        </p>
        <div className="flex justify-center gap-4">
          <Badge variant="secondary" className="text-sm">
            <Calendar className="mr-1 h-3 w-3" />
            最后更新：2024年1月1日
          </Badge>
          <Badge variant="secondary" className="text-sm">
            生效日期：2024年1月1日
          </Badge>
        </div>
      </div>

      {/* 重要提示 */}
      <Alert className="mb-8 max-w-4xl mx-auto">
        <AlertTriangle className="h-4 w-4" />
        <AlertDescription className="text-sm">
          <strong>重要提示：</strong>
          通过使用我们的服务，您表示已阅读、理解并同意本隐私政策。
          如果您不同意本政策的任何部分，请勿使用我们的服务。
        </AlertDescription>
      </Alert>

      {/* 隐私政策内容 */}
      <div className="max-w-4xl mx-auto space-y-8">
        {sections.map((section) => (
          <Card key={section.id} id={section.id}>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg text-primary">
                  {section.icon}
                </div>
                {section.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {section.content.map((subsection, index) => (
                <div key={index}>
                  <h4 className="font-semibold text-lg mb-3 text-primary">
                    {subsection.subtitle}
                  </h4>
                  <ul className="space-y-2">
                    {subsection.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                        <span className="text-muted-foreground leading-relaxed">
                          {item}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}

        {/* 特殊保护措施 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg text-primary">
                <Shield className="h-5 w-5" />
              </div>
              特殊保护措施
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold text-lg mb-3 text-primary">未成年人保护</h4>
              <p className="text-muted-foreground leading-relaxed mb-4">
                我们的服务主要面向成年人。如果您未满18岁，请在家长或监护人同意下使用我们的服务。
                我们不会有意收集未成年人的个人信息。
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-lg mb-3 text-primary">匿名功能</h4>
              <p className="text-muted-foreground leading-relaxed mb-4">
                我们提供匿名发布功能，保护用户在分享敏感职场信息时的隐私。
                匿名内容不会与您的账户公开关联，但我们保留必要的技术手段以防范恶意行为。
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 政策更新 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg text-primary">
                <Calendar className="h-5 w-5" />
              </div>
              政策更新
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed mb-4">
              我们可能会定期更新本隐私政策以反映我们服务的变化或法律要求。
              重大变更会通过以下方式通知您：
            </p>
            <ul className="space-y-2 mb-4">
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                <span className="text-muted-foreground">在网站显著位置发布通知</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                <span className="text-muted-foreground">通过邮件通知已注册用户</span>
              </li>
              <li className="flex items-start gap-2">
                <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                <span className="text-muted-foreground">在用户登录时弹窗提醒</span>
              </li>
            </ul>
            <p className="text-muted-foreground leading-relaxed">
              继续使用我们的服务即表示您接受更新后的政策。
            </p>
          </CardContent>
        </Card>

        {/* 联系我们 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg text-primary">
                <Mail className="h-5 w-5" />
              </div>
              联系我们
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed mb-4">
              如果您对本隐私政策有任何疑问或需要行使您的数据权利，请通过以下方式联系我们：
            </p>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <Mail className="h-4 w-4 text-primary" />
                <div>
                  <p className="font-medium">隐私事务邮箱</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <Users className="h-4 w-4 text-primary" />
                <div>
                  <p className="font-medium">数据保护官</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
              </div>
            </div>
            
            <p className="text-sm text-muted-foreground mt-4">
              我们会在收到您的请求后30天内回复。为了保护您的隐私，我们可能需要验证您的身份。
            </p>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 