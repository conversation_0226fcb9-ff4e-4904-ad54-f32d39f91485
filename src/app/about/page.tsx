import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    Award,
    Calendar,
    Globe,
    Heart,
    MapPin,
    Shield,
    Target,
    TrendingUp,
    Users,
    Zap
} from 'lucide-react'
import { Metadata } from 'next'
import Link from 'next/link'

export const metadata: Metadata = {
  title: '关于我们 - WorkMates',
  description: '了解 WorkMates 的发展历程、团队愿景和价值观'
}

// 团队成员数据
const teamMembers = [
  {
    name: '张伟',
    role: '创始人 & CEO',
    bio: '前阿里巴巴高级产品经理，专注于职场社区产品10年',
    avatar: '',
    expertise: ['产品设计', '团队管理', '战略规划']
  },
  {
    name: '李小雨',
    role: '技术总监',
    bio: '前腾讯资深工程师，在大规模系统架构方面有丰富经验',
    avatar: '',
    expertise: ['系统架构', '技术管理', '性能优化']
  },
  {
    name: '王建国',
    role: '产品总监',
    bio: '前字节跳动产品专家，擅长用户体验设计和数据驱动决策',
    avatar: '',
    expertise: ['用户体验', '数据分析', '产品策略']
  },
  {
    name: '陈美丽',
    role: '运营总监',
    bio: '前美团运营负责人，在社区运营和用户增长方面经验丰富',
    avatar: '',
    expertise: ['社区运营', '用户增长', '内容策略']
  }
]

// 发展里程碑
const milestones = [
  {
    year: '2024',
    title: '正式上线',
    description: 'WorkMates 正式发布，开始为职场人士提供信息交流平台',
    stats: '用户数突破 10,000+'
  },
  {
    year: '2023',
    title: '团队组建',
    description: '核心团队成立，开始产品研发和市场调研',
    stats: '完成种子轮融资'
  },
  {
    year: '2022',
    title: '项目启动',
    description: '项目构思阶段，确定产品定位和发展方向',
    stats: '完成商业计划书'
  }
]

// 价值观数据
const values = [
  {
    icon: <Heart className="h-8 w-8" />,
    title: '真实透明',
    description: '我们致力于创建一个真实、透明的职场信息分享环境，让每个人都能获得可信的职场信息。'
  },
  {
    icon: <Users className="h-8 w-8" />,
    title: '互助共赢',
    description: '鼓励用户分享经验、互相帮助，通过集体智慧帮助每个人在职场中取得成功。'
  },
  {
    icon: <Shield className="h-8 w-8" />,
    title: '隐私保护',
    description: '严格保护用户隐私，提供匿名分享选项，让用户安心分享真实体验。'
  },
  {
    icon: <Zap className="h-8 w-8" />,
    title: '持续创新',
    description: '不断优化产品体验，引入新技术和新功能，为用户提供更好的服务。'
  }
]

export default function AboutPage() {
  return (
    <div className="min-h-screen">
      {/* 英雄区域 */}
      <section className="bg-gradient-to-b from-primary/5 to-background py-20">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-5xl font-bold mb-6">关于 WorkMates</h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
            我们致力于打造中国最真实、最有价值的职场信息分享平台，
            帮助每一位职场人士做出更明智的职业决策。
          </p>
          <div className="flex flex-wrap justify-center gap-4 mb-12">
            <Badge variant="secondary" className="text-lg px-4 py-2">
              <Users className="mr-2 h-4 w-4" />
              50,000+ 活跃用户
            </Badge>
            <Badge variant="secondary" className="text-lg px-4 py-2">
              <TrendingUp className="mr-2 h-4 w-4" />
              10,000+ 企业信息
            </Badge>
            <Badge variant="secondary" className="text-lg px-4 py-2">
              <Award className="mr-2 h-4 w-4" />
              95% 用户满意度
            </Badge>
          </div>
        </div>
      </section>

      <div className="container mx-auto px-4 py-16">
        {/* 我们的使命 */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">我们的使命</h2>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              让职场信息更透明，让职业选择更明智
            </p>
          </div>
          
          <div className="grid md:grid-cols-3 gap-8">
            <Card className="text-center">
              <CardContent className="pt-8">
                <Target className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-3">信息透明化</h3>
                <p className="text-muted-foreground">
                  打破信息不对称，让每个人都能获得真实的职场信息
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="pt-8">
                <Globe className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-3">知识共享</h3>
                <p className="text-muted-foreground">
                  建立开放的知识分享平台，促进职场经验交流
                </p>
              </CardContent>
            </Card>
            
            <Card className="text-center">
              <CardContent className="pt-8">
                <Users className="h-12 w-12 text-primary mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-3">社区建设</h3>
                <p className="text-muted-foreground">
                  构建互助友爱的职场社区，帮助每个人职业发展
                </p>
              </CardContent>
            </Card>
          </div>
        </section>

        {/* 核心价值观 */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">核心价值观</h2>
            <p className="text-xl text-muted-foreground">
              指导我们行动的基本原则
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            {values.map((value, index) => (
              <Card key={index} className="text-center hover:shadow-lg transition-shadow">
                <CardContent className="pt-8">
                  <div className="text-primary mb-4 flex justify-center">
                    {value.icon}
                  </div>
                  <h3 className="text-lg font-semibold mb-3">{value.title}</h3>
                  <p className="text-muted-foreground text-sm leading-relaxed">
                    {value.description}
                  </p>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 团队介绍 */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">核心团队</h2>
            <p className="text-xl text-muted-foreground">
              来自顶级互联网公司的精英团队
            </p>
          </div>
          
          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-8">
            {teamMembers.map((member, index) => (
              <Card key={index} className="text-center">
                <CardContent className="pt-8">
                  <Avatar className="h-20 w-20 mx-auto mb-4">
                    <AvatarImage src={member.avatar} />
                    <AvatarFallback className="text-lg">
                      {member.name.slice(0, 1)}
                    </AvatarFallback>
                  </Avatar>
                  
                  <h3 className="text-lg font-semibold mb-1">{member.name}</h3>
                  <p className="text-primary font-medium mb-3">{member.role}</p>
                  <p className="text-muted-foreground text-sm mb-4 leading-relaxed">
                    {member.bio}
                  </p>
                  
                  <div className="flex flex-wrap gap-1 justify-center">
                    {member.expertise.map((skill) => (
                      <Badge key={skill} variant="outline" className="text-xs">
                        {skill}
                      </Badge>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 发展历程 */}
        <section className="mb-20">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4">发展历程</h2>
            <p className="text-xl text-muted-foreground">
              从想法到现实的成长轨迹
            </p>
          </div>
          
          <div className="space-y-8">
            {milestones.map((milestone, index) => (
              <Card key={index} className="relative">
                <CardContent className="flex flex-col md:flex-row items-start gap-6 p-8">
                  <div className="flex-shrink-0">
                    <div className="w-16 h-16 bg-primary text-primary-foreground rounded-full flex items-center justify-center text-xl font-bold">
                      {milestone.year.slice(-2)}
                    </div>
                  </div>
                  
                  <div className="flex-1">
                    <div className="flex flex-col md:flex-row md:items-center gap-4 mb-2">
                      <h3 className="text-xl font-semibold">{milestone.title}</h3>
                      <Badge variant="secondary" className="w-fit">
                        <Calendar className="mr-1 h-3 w-3" />
                        {milestone.year}
                      </Badge>
                    </div>
                    <p className="text-muted-foreground mb-3">{milestone.description}</p>
                    <p className="text-primary font-medium">{milestone.stats}</p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </section>

        {/* 联系我们 */}
        <section className="text-center">
          <Card className="max-w-2xl mx-auto">
            <CardHeader>
              <CardTitle className="text-2xl">加入我们的旅程</CardTitle>
              <p className="text-muted-foreground">
                如果您对我们的使命感兴趣，欢迎与我们取得联系
              </p>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild>
                  <Link href="/help">
                    了解更多
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="mailto:<EMAIL>">
                    联系我们
                  </Link>
                </Button>
              </div>
              
              <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
                <MapPin className="h-4 w-4" />
                <span>北京市朝阳区望京SOHO</span>
              </div>
            </CardContent>
          </Card>
        </section>
      </div>
    </div>
  )
} 