'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { ArrowLeft, HelpCircle, Home, Search } from 'lucide-react'
import { Metadata } from 'next'
import Link from 'next/link'

export const metadata: Metadata = {
  title: '页面未找到 - WorkMates',
  description: '抱歉，您访问的页面不存在',
}

export default function NotFound() {
  const quickLinks = [
    {
      title: '企业信息',
      description: '查看企业详情、薪资数据和评价',
      href: '/companies',
      icon: '🏢',
    },
    {
      title: '面试经验',
      description: '阅读面试经验和求职技巧',
      href: '/companies/interviews',
      icon: '💼',
    },
    {
      title: '社区论坛',
      description: '参与职场讨论和经验分享',
      href: '/forum',
      icon: '💬',
    },
    {
      title: '帮助中心',
      description: '获取使用指南和常见问题解答',
      href: '/help',
      icon: '❓',
    },
  ]

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-b from-gray-50 to-gray-100">
      <div className="max-w-2xl mx-auto px-4 text-center">
        {/* 404 图标和文字 */}
        <div className="mb-8">
          <div className="text-9xl font-bold text-gray-300 mb-4">404</div>
          <h1 className="text-3xl font-bold text-gray-800 mb-2">页面未找到</h1>
          <p className="text-lg text-gray-600 mb-8">
            抱歉，您访问的页面不存在或已被移动
          </p>
        </div>

        {/* 操作按钮 */}
        <div className="flex flex-col sm:flex-row gap-4 justify-center mb-12">
          <Button asChild size="lg">
            <Link href="/">
              <Home className="mr-2 h-4 w-4" />
              返回首页
            </Link>
          </Button>

          <Button variant="outline" size="lg" asChild>
            <Link href="/search">
              <Search className="mr-2 h-4 w-4" />
              搜索内容
            </Link>
          </Button>

          <Button
            variant="outline"
            size="lg"
            onClick={() => window.history.back()}
          >
            <ArrowLeft className="mr-2 h-4 w-4" />
            返回上页
          </Button>
        </div>

        {/* 快速链接 */}
        <Card className="text-left">
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <HelpCircle className="h-5 w-5" />
              您可能想要：
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {quickLinks.map((link, index) => (
                <Link
                  key={index}
                  href={link.href}
                  className="flex items-start gap-3 p-4 rounded-lg hover:bg-gray-50 transition-colors group"
                >
                  <span className="text-2xl">{link.icon}</span>
                  <div>
                    <h3 className="font-semibold text-gray-800 group-hover:text-primary transition-colors">
                      {link.title}
                    </h3>
                    <p className="text-sm text-gray-600">{link.description}</p>
                  </div>
                </Link>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* 联系支持 */}
        <div className="mt-8 text-sm text-gray-500">
          <p>
            如果您认为这是一个错误，请
            <Link href="/help" className="text-primary hover:underline mx-1">
              联系我们
            </Link>
            报告此问题
          </p>
        </div>
      </div>
    </div>
  )
}
