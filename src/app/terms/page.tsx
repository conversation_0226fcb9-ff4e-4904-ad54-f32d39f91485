import { Alert, AlertDescription } from '@/components/ui/alert'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
    AlertTriangle,
    Ban,
    BookOpen,
    Calendar,
    FileText,
    Gavel,
    Mail,
    Scale,
    Shield,
    UserCheck
} from 'lucide-react'
import { Metadata } from 'next'

export const metadata: Metadata = {
  title: '服务条款 - WorkMates',
  description: '查看 WorkMates 平台的使用条款、用户责任和相关法律条款'
}

// 服务条款章节数据
const sections = [
  {
    id: 'acceptance',
    title: '条款接受',
    icon: <UserCheck className="h-5 w-5" />,
    content: [
      {
        subtitle: '条款生效',
        items: [
          '通过访问或使用 WorkMates 服务，您表示已阅读、理解并同意受本服务条款约束',
          '如果您不同意本条款的任何部分，请立即停止使用我们的服务',
          '我们保留随时修改这些条款的权利，修改后的条款将在发布后立即生效',
          '继续使用服务将被视为接受修改后的条款'
        ]
      },
      {
        subtitle: '用户资格',
        items: [
          '您必须年满18周岁才能使用我们的服务',
          '未满18周岁的用户需要在家长或监护人同意下使用',
          '您必须提供真实、准确的个人信息',
          '一个人只能注册一个账户'
        ]
      }
    ]
  },
  {
    id: 'service-description',
    title: '服务描述',
    icon: <BookOpen className="h-5 w-5" />,
    content: [
      {
        subtitle: '平台功能',
        items: [
          'WorkMates 是一个职场信息分享和交流平台',
          '提供企业信息查看、薪资数据分享、面试经验交流等功能',
          '支持用户发布和讨论与职场相关的内容',
          '我们努力保持信息的准确性，但不对用户生成内容的真实性承担责任'
        ]
      },
      {
        subtitle: '服务范围',
        items: [
          '我们保留随时修改、暂停或终止服务的权利',
          '某些功能可能需要付费或注册才能使用',
          '我们不保证服务的持续可用性或不间断性',
          '技术维护期间可能会暂时无法访问某些功能'
        ]
      }
    ]
  },
  {
    id: 'user-conduct',
    title: '用户行为规范',
    icon: <Shield className="h-5 w-5" />,
    content: [
      {
        subtitle: '允许的行为',
        items: [
          '分享真实的工作经验和职场信息',
          '参与建设性的讨论和交流',
          '尊重其他用户的隐私和权利',
          '遵守相关法律法规和平台规则',
          '报告违规内容和行为'
        ]
      },
      {
        subtitle: '禁止的行为',
        items: [
          '发布虚假、误导性或恶意信息',
          '进行人身攻击、骚扰或歧视性言论',
          '发布涉及政治敏感、色情、暴力等不当内容',
          '恶意营销、垃圾信息或未经授权的广告',
          '侵犯他人知识产权或隐私权',
          '试图破坏系统安全或干扰正常运行',
          '创建虚假账户或冒充他人身份'
        ]
      }
    ]
  },
  {
    id: 'content-policy',
    title: '内容政策',
    icon: <FileText className="h-5 w-5" />,
    content: [
      {
        subtitle: '内容所有权',
        items: [
          '您保留对自己发布内容的所有权',
          '通过发布内容，您授予我们使用、修改、展示该内容的非排他性许可',
          '我们不会将您的内容用于商业目的（除非获得明确授权）',
          '您有权随时删除自己发布的内容'
        ]
      },
      {
        subtitle: '内容审核',
        items: [
          '我们保留审核、编辑或删除任何内容的权利',
          '违反平台规则的内容将被删除',
          '重复违规的用户可能面临账户限制或封禁',
          '我们采用人工审核和自动化工具相结合的方式'
        ]
      }
    ]
  },
  {
    id: 'privacy-data',
    title: '隐私与数据',
    icon: <Shield className="h-5 w-5" />,
    content: [
      {
        subtitle: '隐私保护',
        items: [
          '我们严格按照隐私政策处理您的个人信息',
          '支持匿名发布功能，保护用户隐私',
          '不会出售用户个人信息给第三方',
          '采用行业标准的安全措施保护数据'
        ]
      },
      {
        subtitle: '数据使用',
        items: [
          '我们可能使用匿名化的数据进行分析和改进服务',
          '用于生成行业报告和趋势分析',
          '配合法律要求提供必要的用户信息',
          '在业务转让时可能涉及数据转移'
        ]
      }
    ]
  },
  {
    id: 'liability',
    title: '责任限制',
    icon: <Scale className="h-5 w-5" />,
    content: [
      {
        subtitle: '服务免责',
        items: [
          '我们的服务按"现状"提供，不提供任何形式的担保',
          '不保证服务的准确性、完整性或可靠性',
          '不对用户生成的内容承担责任',
          '不对因使用服务而产生的任何损失负责'
        ]
      },
      {
        subtitle: '损害赔偿',
        items: [
          '在法律允许的最大范围内，我们的责任限制在您支付的服务费用',
          '不对间接损失、利润损失或数据丢失承担责任',
          '用户应对自己的行为承担全部责任',
          '用户同意赔偿因违反条款而给我们造成的损失'
        ]
      }
    ]
  },
  {
    id: 'termination',
    title: '服务终止',
    icon: <Ban className="h-5 w-5" />,
    content: [
      {
        subtitle: '账户终止',
        items: [
          '您可以随时删除自己的账户',
          '我们保留因违规行为终止用户账户的权利',
          '账户终止后，相关数据将按照隐私政策处理',
          '某些法律要求的数据可能会保留更长时间'
        ]
      },
      {
        subtitle: '服务中断',
        items: [
          '我们保留随时暂停或终止服务的权利',
          '会提前通知重大服务变更',
          '紧急情况下可能无法提前通知',
          '服务终止后会协助用户导出重要数据'
        ]
      }
    ]
  }
]

export default function TermsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面头部 */}
      <div className="text-center mb-12">
        <div className="flex justify-center mb-6">
          <div className="p-4 bg-primary/10 rounded-full">
            <Scale className="h-12 w-12 text-primary" />
          </div>
        </div>
        <h1 className="text-4xl font-bold mb-4">服务条款</h1>
        <p className="text-xl text-muted-foreground mb-6 max-w-3xl mx-auto">
          WorkMates 服务条款规定了您使用我们平台的权利和义务。
          请仔细阅读这些条款，它们是您与我们之间的法律协议。
        </p>
        <div className="flex justify-center gap-4">
          <Badge variant="secondary" className="text-sm">
            <Calendar className="mr-1 h-3 w-3" />
            最后更新：2024年1月1日
          </Badge>
          <Badge variant="secondary" className="text-sm">
            生效日期：2024年1月1日
          </Badge>
        </div>
      </div>

      {/* 重要提示 */}
      <Alert className="mb-8 max-w-4xl mx-auto border-orange-200 bg-orange-50">
        <AlertTriangle className="h-4 w-4 text-orange-600" />
        <AlertDescription className="text-sm text-orange-800">
          <strong>重要提示：</strong>
          这些条款具有法律约束力。如果您不同意任何条款，请不要使用我们的服务。
          使用我们的服务即表示您同意遵守这些条款。
        </AlertDescription>
      </Alert>

      {/* 服务条款内容 */}
      <div className="max-w-4xl mx-auto space-y-8">
        {sections.map((section) => (
          <Card key={section.id} id={section.id}>
            <CardHeader>
              <CardTitle className="flex items-center gap-3">
                <div className="p-2 bg-primary/10 rounded-lg text-primary">
                  {section.icon}
                </div>
                {section.title}
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-6">
              {section.content.map((subsection, index) => (
                <div key={index}>
                  <h4 className="font-semibold text-lg mb-3 text-primary">
                    {subsection.subtitle}
                  </h4>
                  <ul className="space-y-2">
                    {subsection.items.map((item, itemIndex) => (
                      <li key={itemIndex} className="flex items-start gap-2">
                        <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                        <span className="text-muted-foreground leading-relaxed">
                          {item}
                        </span>
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </CardContent>
          </Card>
        ))}

        {/* 争议解决 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg text-primary">
                <Gavel className="h-5 w-5" />
              </div>
              争议解决
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold text-lg mb-3 text-primary">法律适用</h4>
              <p className="text-muted-foreground leading-relaxed mb-4">
                本服务条款受中华人民共和国法律管辖。
                任何争议应首先通过友好协商解决。
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-lg mb-3 text-primary">争议解决程序</h4>
              <ul className="space-y-2 mb-4">
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <span className="text-muted-foreground">首先通过平台客服系统提交争议</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <span className="text-muted-foreground">我们会在7个工作日内回复并协调解决</span>
                </li>
                <li className="flex items-start gap-2">
                  <div className="w-1.5 h-1.5 bg-primary rounded-full mt-2 flex-shrink-0" />
                  <span className="text-muted-foreground">无法协商解决的争议提交北京仲裁委员会仲裁</span>
                </li>
              </ul>
            </div>
          </CardContent>
        </Card>

        {/* 其他重要条款 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg text-primary">
                <FileText className="h-5 w-5" />
              </div>
              其他重要条款
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h4 className="font-semibold text-lg mb-3 text-primary">条款修改</h4>
              <p className="text-muted-foreground leading-relaxed mb-4">
                我们保留随时修改这些服务条款的权利。重大修改会通过邮件、网站公告等方式通知用户。
                修改后的条款在发布后即生效，继续使用服务表示接受新条款。
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-lg mb-3 text-primary">可分离性</h4>
              <p className="text-muted-foreground leading-relaxed mb-4">
                如果本条款的任何部分被认定为无效或不可执行，其余部分仍然有效。
                无效部分将被替换为在法律允许范围内最接近原意的条款。
              </p>
            </div>
            
            <div>
              <h4 className="font-semibold text-lg mb-3 text-primary">完整协议</h4>
              <p className="text-muted-foreground leading-relaxed mb-4">
                本服务条款与隐私政策共同构成您与我们之间的完整协议，
                取代之前的所有协议、声明或承诺。
              </p>
            </div>
          </CardContent>
        </Card>

        {/* 联系信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-3">
              <div className="p-2 bg-primary/10 rounded-lg text-primary">
                <Mail className="h-5 w-5" />
              </div>
              联系我们
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground leading-relaxed mb-4">
              如果您对本服务条款有任何疑问或需要法律帮助，请通过以下方式联系我们：
            </p>
            <div className="space-y-3">
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <Mail className="h-4 w-4 text-primary" />
                <div>
                  <p className="font-medium">法务邮箱</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
              </div>
              
              <div className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <FileText className="h-4 w-4 text-primary" />
                <div>
                  <p className="font-medium">客服支持</p>
                  <p className="text-sm text-muted-foreground"><EMAIL></p>
                </div>
              </div>
            </div>
            
            <div className="mt-6 p-4 bg-blue-50 rounded-lg border border-blue-200">
              <p className="text-sm text-blue-800">
                <strong>法律实体：</strong>北京工作伙伴科技有限公司<br />
                <strong>注册地址：</strong>北京市朝阳区望京SOHO T1 A座 1801室<br />
                <strong>统一社会信用代码：</strong>91110000XXXXXXXXXX
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 