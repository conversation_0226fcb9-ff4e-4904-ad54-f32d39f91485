'use client'

import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { signIn, signOut, useSession } from 'next-auth/react'
import Link from 'next/link'
import { useState } from 'react'

/**
 * 认证系统测试页面
 * 用于验证 NextAuth.js 集成是否正常工作
 */
export default function AuthTestPage() {
  const { data: session, status } = useSession()
  const [isLoading, setIsLoading] = useState(false)

  const handleSignIn = async (provider: string) => {
    setIsLoading(true)
    try {
      await signIn(provider)
    } catch (error) {
      console.error('登录失败:', error)
    }
    setIsLoading(false)
  }

  const handleSignOut = async () => {
    setIsLoading(true)
    try {
      await signOut()
    } catch (error) {
      console.error('登出失败:', error)
    }
    setIsLoading(false)
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <h1 className="text-3xl font-bold mb-8">认证系统测试页面</h1>
      
      <div className="grid gap-6 md:grid-cols-2">
        {/* 认证状态卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>认证状态</CardTitle>
            <CardDescription>当前用户的认证状态信息</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <p className="text-sm">
                <span className="font-medium">状态：</span>
                <span className={`ml-2 px-2 py-1 rounded text-xs ${
                  status === 'authenticated' ? 'bg-green-100 text-green-800' :
                  status === 'loading' ? 'bg-yellow-100 text-yellow-800' :
                  'bg-red-100 text-red-800'
                }`}>
                  {status === 'authenticated' ? '已登录' :
                   status === 'loading' ? '加载中...' : '未登录'}
                </span>
              </p>
              
              {session && (
                <>
                  <p className="text-sm">
                    <span className="font-medium">用户ID：</span>
                    <span className="ml-2 font-mono text-xs">{session.user?.id || 'N/A'}</span>
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">邮箱：</span>
                    <span className="ml-2">{session.user?.email || 'N/A'}</span>
                  </p>
                  <p className="text-sm">
                    <span className="font-medium">用户名：</span>
                    <span className="ml-2">{session.user?.name || 'N/A'}</span>
                  </p>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* 操作卡片 */}
        <Card>
          <CardHeader>
            <CardTitle>测试操作</CardTitle>
            <CardDescription>测试各种认证功能</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {status === 'unauthenticated' ? (
                <>
                  <Button 
                    onClick={() => handleSignIn('google')}
                    disabled={isLoading}
                    className="w-full"
                    variant="outline"
                  >
                    使用 Google 登录
                  </Button>
                  
                  <Link href="/auth/login">
                    <Button className="w-full" variant="default">
                      前往登录页面
                    </Button>
                  </Link>
                  
                  <Link href="/auth/register">
                    <Button className="w-full" variant="secondary">
                      前往注册页面
                    </Button>
                  </Link>
                </>
              ) : (
                <>
                  <Button 
                    onClick={handleSignOut}
                    disabled={isLoading}
                    className="w-full"
                    variant="destructive"
                  >
                    登出
                  </Button>
                  
                  <Link href="/profile">
                    <Button className="w-full" variant="outline">
                      查看个人资料
                    </Button>
                  </Link>
                </>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Session 数据展示 */}
        {session && (
          <Card className="md:col-span-2">
            <CardHeader>
              <CardTitle>Session 数据</CardTitle>
              <CardDescription>完整的会话数据（调试用）</CardDescription>
            </CardHeader>
            <CardContent>
              <pre className="bg-gray-100 p-4 rounded overflow-x-auto text-xs">
                {JSON.stringify(session, null, 2)}
              </pre>
            </CardContent>
          </Card>
        )}

        {/* 测试结果 */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>测试清单</CardTitle>
            <CardDescription>检查各项功能是否正常</CardDescription>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <label className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  checked={status !== 'loading'}
                  readOnly
                  className="rounded"
                />
                <span className="text-sm">✓ NextAuth.js 正确加载</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  checked={status === 'authenticated'}
                  readOnly
                  className="rounded"
                />
                <span className="text-sm">✓ 用户登录状态</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  checked={!!session?.user?.id}
                  readOnly
                  className="rounded"
                />
                <span className="text-sm">✓ 用户ID获取</span>
              </label>
              
              <label className="flex items-center space-x-2">
                <input 
                  type="checkbox" 
                  checked={!!session?.user?.email}
                  readOnly
                  className="rounded"
                />
                <span className="text-sm">✓ 用户邮箱获取</span>
              </label>
            </div>
            
            <div className="mt-4 p-4 bg-blue-50 rounded">
              <p className="text-sm text-blue-800">
                <strong>提示：</strong>请确保已经运行了 setup-google-oauth.js 脚本并正确配置了环境变量。
              </p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
} 