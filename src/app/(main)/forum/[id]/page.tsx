import { Suspense } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  MessageSquare, 
  Share2, 
  Bookmark, 
  ThumbsUp, 
  Clock, 
  Eye,
  Flag,
  Reply
} from 'lucide-react'

/**
 * 帖子详情页面
 * 展示帖子完整内容、评论列表和相关功能
 */
export default async function PostDetailPage({
  params,
}: {
  params: Promise<{ id: string }>
}) {
  const { id } = await params

  // 模拟帖子数据
  const post = {
    id: parseInt(id),
    title: '大厂裁员潮下，普通程序员该如何应对？',
    content: `
近期互联网行业裁员消息不断，从阿里、腾讯到字节跳动，各大厂都在进行人员优化。作为一名在大厂工作了5年的程序员，我想分享一些自己的思考和建议。

## 保持技术敏感度

在这个快速变化的时代，技术更新迭代很快。我们需要：

1. **持续学习新技术**：关注行业趋势，学习热门技术栈
2. **深度优于广度**：在某个领域做到专精
3. **培养系统思维**：不仅仅是编码，要理解业务和架构

## 建立个人品牌

- 在GitHub上维护开源项目
- 写技术博客分享经验
- 参与技术社区讨论
- 建立自己的影响力

## 财务规划

- 保持6-12个月的生活开销储备
- 分散投资，不要把鸡蛋放在一个篮子里
- 考虑副业或者被动收入

## 心态调整

裁员并不代表个人能力问题，很多时候是公司战略调整。保持积极心态，把这当作新的机遇。

大家怎么看？欢迎讨论！
    `,
    author: {
      id: 1,
      name: '资深码农',
      avatar: '/avatar1.jpg',
      company: '某大厂',
      position: '高级工程师',
    },
    category: '职场讨论',
    tags: ['职业规划', '互联网', '裁员', '程序员'],
    likes: 256,
    comments: 89,
    views: 3420,
    bookmarks: 45,
    createdAt: '2024-01-20T10:30:00Z',
    updatedAt: '2024-01-20T10:30:00Z',
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid gap-6 lg:grid-cols-4">
        {/* 主内容区域 */}
        <div className="lg:col-span-3 space-y-6">
          {/* 帖子内容卡片 */}
          <Card>
            <CardHeader>
              {/* 标题和分类 */}
              <div className="space-y-4">
                <div className="flex items-center gap-2">
                  <Badge variant="secondary">{post.category}</Badge>
                  {post.tags.map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>
                <CardTitle className="text-2xl lg:text-3xl">{post.title}</CardTitle>
              </div>

              {/* 作者信息 */}
              <div className="flex items-center justify-between">
                <div className="flex items-center gap-3">
                  <Avatar className="h-12 w-12">
                    <AvatarImage src={post.author.avatar} alt={post.author.name} />
                    <AvatarFallback>{post.author.name[0]}</AvatarFallback>
                  </Avatar>
                  <div>
                    <p className="font-semibold">{post.author.name}</p>
                    <p className="text-sm text-gray-600">
                      {post.author.position} @ {post.author.company}
                    </p>
                  </div>
                </div>
                <div className="text-sm text-gray-600 flex items-center gap-4">
                  <span className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    2小时前
                  </span>
                  <span className="flex items-center gap-1">
                    <Eye className="h-4 w-4" />
                    {post.views}
                  </span>
                </div>
              </div>
            </CardHeader>

            <CardContent>
              {/* 帖子内容 */}
              <div className="prose prose-gray max-w-none mb-6">
                <div className="whitespace-pre-wrap">{post.content}</div>
              </div>

              {/* 互动按钮 */}
              <div className="flex items-center justify-between pt-6 border-t border-gray-200">
                <div className="flex items-center gap-4">
                  <Button variant="ghost" size="sm" className="gap-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50">
                    <ThumbsUp className="h-4 w-4" />
                    <span className="font-medium">{post.likes}</span>
                  </Button>
                  <Button variant="ghost" size="sm" className="gap-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50">
                    <MessageSquare className="h-4 w-4" />
                    <span className="font-medium">{post.comments}</span>
                  </Button>
                  <Button variant="ghost" size="sm" className="gap-2 text-gray-700 hover:text-blue-600 hover:bg-blue-50">
                    <Bookmark className="h-4 w-4" />
                    <span className="font-medium">{post.bookmarks}</span>
                  </Button>
                </div>
                <div className="flex items-center gap-2">
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-blue-600 hover:bg-blue-50">
                    <Share2 className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="text-gray-600 hover:text-red-600 hover:bg-red-50">
                    <Flag className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* 评论区域 */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                评论 ({post.comments})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <Suspense fallback={<div>加载评论中...</div>}>
                <CommentSection postId={post.id} />
              </Suspense>
            </CardContent>
          </Card>
        </div>

        {/* 侧边栏 */}
        <div className="lg:col-span-1 space-y-6">
          {/* 作者信息卡片 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">作者信息</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex flex-col items-center text-center space-y-4">
                <Avatar className="h-20 w-20">
                  <AvatarImage src={post.author.avatar} alt={post.author.name} />
                  <AvatarFallback>{post.author.name[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <p className="font-semibold">{post.author.name}</p>
                  <p className="text-sm text-gray-600">
                    {post.author.position}
                  </p>
                  <p className="text-sm text-gray-600">
                    @ {post.author.company}
                  </p>
                </div>
                <Button className="w-full" size="sm">关注</Button>
              </div>
            </CardContent>
          </Card>

          {/* 相关推荐 */}
          <Card>
            <CardHeader>
              <CardTitle className="text-lg">相关推荐</CardTitle>
            </CardHeader>
            <CardContent>
              <RelatedPosts />
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}

/**
 * 评论区组件
 */
function CommentSection({ postId }: { postId: number }) {
  // TODO: 根据 postId 从 API 获取评论数据
  console.log('Loading comments for post:', postId)
  
  // 模拟评论数据
  const comments = [
    {
      id: 1,
      content: '说得很好！特别是关于技术敏感度的部分，我觉得在这个时代确实需要持续学习。',
      author: {
        name: '小王',
        avatar: '/avatar2.jpg',
        company: '某互联网公司',
      },
      likes: 12,
      replies: 3,
      createdAt: '1小时前',
    },
    {
      id: 2,
      content: '财务规划这块很重要，我就是因为没有足够的储备，现在找工作压力很大。',
      author: {
        name: '求职中',
        avatar: '/avatar3.jpg',
        company: '前某公司',
      },
      likes: 8,
      replies: 1,
      createdAt: '30分钟前',
    },
  ]

  return (
    <div className="space-y-6">
      {/* 发表评论 */}
      <div className="border rounded-lg p-4">
        <textarea
          placeholder="写下你的想法..."
          className="w-full h-24 p-3 border rounded-lg resize-none focus:outline-none focus:ring-2 focus:ring-primary"
        />
        <div className="flex justify-between items-center mt-3">
          <p className="text-sm text-gray-600">支持 Markdown 语法</p>
          <Button>发表评论</Button>
        </div>
      </div>

      {/* 评论列表 */}
      <div className="space-y-4">
        {comments.map(comment => (
          <div key={comment.id} className="border-l-2 border-gray-100 pl-4">
            <div className="flex items-start gap-3">
              <Avatar className="h-8 w-8">
                <AvatarImage src={comment.author.avatar} alt={comment.author.name} />
                <AvatarFallback>{comment.author.name[0]}</AvatarFallback>
              </Avatar>
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-1">
                  <span className="font-semibold text-sm">{comment.author.name}</span>
                  <span className="text-xs text-gray-600">@ {comment.author.company}</span>
                  <span className="text-xs text-gray-500">{comment.createdAt}</span>
                </div>
                <p className="text-sm mb-2">{comment.content}</p>
                <div className="flex items-center gap-4 text-xs">
                  <button className="flex items-center gap-1 text-gray-600 hover:text-primary">
                    <ThumbsUp className="h-3 w-3" />
                    {comment.likes}
                  </button>
                  <button className="flex items-center gap-1 text-gray-600 hover:text-primary">
                    <Reply className="h-3 w-3" />
                    回复
                  </button>
                  {comment.replies > 0 && (
                    <button className="text-primary">
                      查看 {comment.replies} 条回复
                    </button>
                  )}
                </div>
              </div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

/**
 * 相关推荐组件
 */
function RelatedPosts() {
  const relatedPosts = [
    {
      id: 2,
      title: '如何在技术面试中脱颖而出？',
      views: 1240,
      comments: 23,
    },
    {
      id: 3,
      title: '程序员的职业发展路径',
      views: 890,
      comments: 15,
    },
    {
      id: 4,
      title: '远程工作的优缺点分析',
      views: 567,
      comments: 8,
    },
  ]

  return (
    <div className="space-y-3">
      {relatedPosts.map(post => (
        <div key={post.id} className="p-3 border rounded-lg hover:bg-gray-50 cursor-pointer">
          <h4 className="font-medium text-sm mb-2 line-clamp-2">{post.title}</h4>
          <div className="flex items-center gap-3 text-xs text-gray-600">
            <span className="flex items-center gap-1">
              <Eye className="h-3 w-3" />
              {post.views}
            </span>
            <span className="flex items-center gap-1">
              <MessageSquare className="h-3 w-3" />
              {post.comments}
            </span>
          </div>
        </div>
      ))}
    </div>
  )
} 