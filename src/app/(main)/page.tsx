import { Suspense } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import {
  Card,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import {
  Building2,
  Users,
  MessageSquare,
  TrendingUp,
  Search,
  Star,
} from 'lucide-react'

export default function HomePage() {
  return (
    <div className="min-h-screen">
      {/* Hero Section */}
      <section className="bg-gradient-to-r from-blue-600 to-purple-600 text-white">
        <div className="container mx-auto px-4 py-20">
          <div className="text-center">
            <h1 className="text-4xl md:text-6xl font-bold mb-6">WorkMates</h1>
            <p className="text-xl md:text-2xl mb-8 text-blue-100">
              专为打工人打造的职场信息分享与交流社区
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button asChild size="lg" variant="secondary">
                <Link href="/companies">探索企业信息</Link>
              </Button>
              <Button asChild size="lg" variant="secondary">
                <Link href="/forum">加入讨论</Link>
              </Button>
            </div>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">
              为什么选择 WorkMates？
            </h2>
            <p className="text-xl text-gray-600">
              真实透明的职场信息，助你做出更明智的职业决策
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <Building2 className="h-12 w-12 text-blue-600 mb-4" />
                <CardTitle className="text-gray-900">企业信息查询</CardTitle>
                <CardDescription className="text-gray-600">
                  全面的企业档案，包含薪资、福利、工作环境等真实信息
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <MessageSquare className="h-12 w-12 text-green-600 mb-4" />
                <CardTitle className="text-gray-900">面经分享</CardTitle>
                <CardDescription className="text-gray-600">
                  真实的面试经验分享，帮你更好地准备面试
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Users className="h-12 w-12 text-purple-600 mb-4" />
                <CardTitle className="text-gray-900">职场交流</CardTitle>
                <CardDescription className="text-gray-600">
                  活跃的职场社区，与同行交流经验和见解
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <TrendingUp className="h-12 w-12 text-orange-600 mb-4" />
                <CardTitle className="text-gray-900">薪资透明</CardTitle>
                <CardDescription className="text-gray-600">
                  匿名薪资数据库，了解真实的市场行情
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Search className="h-12 w-12 text-red-600 mb-4" />
                <CardTitle className="text-gray-900">智能搜索</CardTitle>
                <CardDescription className="text-gray-600">
                  快速找到你关心的企业和职位信息
                </CardDescription>
              </CardHeader>
            </Card>

            <Card>
              <CardHeader>
                <Star className="h-12 w-12 text-yellow-600 mb-4" />
                <CardTitle className="text-gray-900">评分体系</CardTitle>
                <CardDescription className="text-gray-600">
                  多维度企业评分，全方位了解企业状况
                </CardDescription>
              </CardHeader>
            </Card>
          </div>
        </div>
      </section>

      {/* Popular Companies Section */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-bold mb-4">热门企业</h2>
            <p className="text-xl text-gray-600">查看最受关注的企业信息</p>
          </div>

          <Suspense fallback={<div>加载中...</div>}>
            <PopularCompanies />
          </Suspense>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-blue-600 text-white">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-3xl md:text-4xl font-bold mb-4">
            加入 WorkMates 社区
          </h2>
          <p className="text-xl mb-8 text-blue-100">
            与数万职场人士一起分享经验，获取真实的职场信息
          </p>
          <Button asChild size="lg" variant="secondary">
            <Link href="/auth/register">立即注册</Link>
          </Button>
        </div>
      </section>
    </div>
  )
}

function PopularCompanies() {
  // 这里应该从数据库获取热门企业数据
  const companies = [
    { name: '阿里巴巴', rating: 4.2, reviews: 1250 },
    { name: '腾讯', rating: 4.1, reviews: 980 },
    { name: '字节跳动', rating: 4.3, reviews: 1100 },
    { name: '美团', rating: 3.9, reviews: 750 },
  ]

  return (
    <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
      {companies.map(company => (
        <Card
          key={company.name}
          className="hover:shadow-lg transition-shadow cursor-pointer"
        >
          <CardHeader>
            <CardTitle className="text-lg text-gray-900">{company.name}</CardTitle>
            <div className="flex items-center gap-2">
              <div className="flex items-center">
                <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                <span className="ml-1 font-semibold text-gray-900">{company.rating}</span>
              </div>
              <Badge variant="secondary">{company.reviews} 条评价</Badge>
            </div>
          </CardHeader>
        </Card>
      ))}
    </div>
  )
}
