'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '@/components/ui/card'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import {
  Mail,
  Calendar,
  MapPin,
  Briefcase,
  Edit,
  MessageSquare,
  Star,
  Bookmark,
  Settings,
  LogOut,
} from 'lucide-react'

/**
 * 个人中心页面
 * 展示用户个人信息、发布的内容、收藏等
 */
export default function ProfilePage() {
  const [activeTab, setActiveTab] = useState('posts')

  // 模拟用户数据
  const user = {
    id: 1,
    name: '张三',
    username: 'zhang<PERSON>',
    email: '<EMAIL>',
    avatar: '/avatar.jpg',
    bio: '一个热爱分享的职场人，专注于互联网行业',
    location: '北京',
    company: '某互联网公司',
    position: '高级工程师',
    joinDate: '2024-01-15',
    stats: {
      posts: 42,
      comments: 156,
      likes: 523,
      followers: 89,
      following: 126,
    },
  }

  return (
    <div className="container mx-auto px-4 py-8">
      <div className="grid gap-6 md:grid-cols-3">
        {/* 左侧 - 用户信息卡片 */}
        <div className="md:col-span-1">
          <Card>
            <CardHeader>
              <div className="flex flex-col items-center space-y-4">
                {/* 头像 */}
                <Avatar className="h-24 w-24">
                  <AvatarImage src={user.avatar} alt={user.name} />
                  <AvatarFallback>{user.name[0]}</AvatarFallback>
                </Avatar>

                {/* 用户名和简介 */}
                <div className="text-center">
                  <h2 className="text-2xl font-bold">{user.name}</h2>
                  <p className="text-sm text-gray-600">@{user.username}</p>
                  <p className="mt-2 text-sm">{user.bio}</p>
                </div>

                {/* 编辑按钮 */}
                <Button variant="outline" className="w-full">
                  <Edit className="mr-2 h-4 w-4" />
                  编辑资料
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              {/* 用户信息列表 */}
              <div className="space-y-3">
                <div className="flex items-center gap-2 text-sm">
                  <Mail className="h-4 w-4 text-gray-400" />
                  <span>{user.email}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <MapPin className="h-4 w-4 text-gray-400" />
                  <span>{user.location}</span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Briefcase className="h-4 w-4 text-gray-400" />
                  <span>
                    {user.position} @ {user.company}
                  </span>
                </div>
                <div className="flex items-center gap-2 text-sm">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span>
                    加入于 {new Date(user.joinDate).toLocaleDateString()}
                  </span>
                </div>
              </div>

              {/* 统计数据 */}
              <div className="mt-6 grid grid-cols-3 gap-2 text-center">
                <div>
                  <p className="text-2xl font-bold">{user.stats.posts}</p>
                  <p className="text-xs text-gray-600">帖子</p>
                </div>
                <div>
                  <p className="text-2xl font-bold">{user.stats.followers}</p>
                  <p className="text-xs text-gray-600">关注者</p>
                </div>
                <div>
                  <p className="text-2xl font-bold">{user.stats.following}</p>
                  <p className="text-xs text-gray-600">关注中</p>
                </div>
              </div>

              {/* 操作按钮 */}
              <div className="mt-6 space-y-2">
                <Button variant="outline" className="w-full justify-start">
                  <Settings className="mr-2 h-4 w-4" />
                  账户设置
                </Button>
                <Button
                  variant="outline"
                  className="w-full justify-start text-red-600 hover:text-red-700"
                >
                  <LogOut className="mr-2 h-4 w-4" />
                  退出登录
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* 右侧 - 内容标签页 */}
        <div className="md:col-span-2">
          <Tabs value={activeTab} onValueChange={setActiveTab}>
            <TabsList className="grid w-full grid-cols-4">
              <TabsTrigger value="posts">
                <MessageSquare className="mr-2 h-4 w-4" />
                帖子
              </TabsTrigger>
              <TabsTrigger value="comments">
                <MessageSquare className="mr-2 h-4 w-4" />
                评论
              </TabsTrigger>
              <TabsTrigger value="likes">
                <Star className="mr-2 h-4 w-4" />
                点赞
              </TabsTrigger>
              <TabsTrigger value="bookmarks">
                <Bookmark className="mr-2 h-4 w-4" />
                收藏
              </TabsTrigger>
            </TabsList>

            {/* 帖子内容 */}
            <TabsContent value="posts" className="space-y-4">
              <PostList />
            </TabsContent>

            {/* 评论内容 */}
            <TabsContent value="comments" className="space-y-4">
              <CommentList />
            </TabsContent>

            {/* 点赞内容 */}
            <TabsContent value="likes" className="space-y-4">
              <div className="text-center py-12 text-gray-600">
                <Star className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p>你点赞的内容会显示在这里</p>
              </div>
            </TabsContent>

            {/* 收藏内容 */}
            <TabsContent value="bookmarks" className="space-y-4">
              <div className="text-center py-12 text-gray-600">
                <Bookmark className="mx-auto h-12 w-12 text-gray-400 mb-4" />
                <p>你收藏的内容会显示在这里</p>
              </div>
            </TabsContent>
          </Tabs>
        </div>
      </div>
    </div>
  )
}

/**
 * 用户发布的帖子列表
 */
function PostList() {
  const posts = [
    {
      id: 1,
      title: '分享一下我的面试经验',
      content: '最近参加了几家大厂的面试，想和大家分享一下...',
      category: '面经分享',
      likes: 45,
      comments: 12,
      createdAt: '2天前',
    },
    {
      id: 2,
      title: '关于加班文化的一些思考',
      content: '在互联网公司工作了几年，对加班文化有一些自己的看法...',
      category: '职场讨论',
      likes: 89,
      comments: 34,
      createdAt: '1周前',
    },
  ]

  return (
    <div className="space-y-4">
      {posts.map(post => (
        <Card key={post.id}>
          <CardHeader>
            <div className="flex items-start justify-between">
              <div>
                <CardTitle className="text-lg">{post.title}</CardTitle>
                <CardDescription className="mt-1">
                  {post.content}
                </CardDescription>
              </div>
              <Badge variant="secondary">{post.category}</Badge>
            </div>
          </CardHeader>
          <CardContent>
            <div className="flex items-center gap-4 text-sm text-gray-600">
              <span className="flex items-center gap-1">
                <Star className="h-3 w-3" />
                {post.likes}
              </span>
              <span className="flex items-center gap-1">
                <MessageSquare className="h-3 w-3" />
                {post.comments}
              </span>
              <span>{post.createdAt}</span>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

/**
 * 用户发布的评论列表
 */
function CommentList() {
  const comments = [
    {
      id: 1,
      postTitle: '大厂裁员潮下，普通程序员该如何应对？',
      content: '我觉得最重要的是保持学习，提升自己的核心竞争力...',
      createdAt: '3小时前',
    },
    {
      id: 2,
      postTitle: '如何优雅地拒绝加班？',
      content: '可以试试和领导沟通，说明自己的工作效率和产出...',
      createdAt: '1天前',
    },
  ]

  return (
    <div className="space-y-4">
      {comments.map(comment => (
        <Card key={comment.id}>
          <CardHeader>
            <CardTitle className="text-sm text-gray-600">
              评论于《{comment.postTitle}》
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-sm">{comment.content}</p>
            <p className="mt-2 text-xs text-gray-600">{comment.createdAt}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
