'use client'

import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
    Building2,
    DollarSign,
    ExternalLink,
    Grid3X3,
    List,
    MapPin,
    MessageSquare,
    Search,
    Star,
    ThumbsUp,
    Trash2
} from 'lucide-react'
import Link from 'next/link'
import { useState } from 'react'

/**
 * 用户收藏夹页面
 * 展示用户收藏的各种内容
 */
export default function FavoritesPage() {
  const [viewMode, setViewMode] = useState<'grid' | 'list'>('grid')
  const [selectedItems, setSelectedItems] = useState<string[]>([])

  const handleSelectItem = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    )
  }

  const handleSelectAll = (items: any[]) => {
    if (selectedItems.length === items.length) {
      setSelectedItems([])
    } else {
      setSelectedItems(items.map(item => item.id))
    }
  }

  const handleRemoveFavorites = () => {
    // TODO: 实际删除收藏逻辑
    console.log('移除收藏:', selectedItems)
    setSelectedItems([])
  }

  // 模拟收藏数据
  const favoriteData = {
    companies: [
      {
        id: '1',
        name: '阿里巴巴',
        industry: '互联网',
        location: '杭州',
        size: '10000+',
        rating: 4.2,
        reviewCount: 1250,
        salaryLevel: '高',
        tags: ['大厂', '福利好', '技术牛'],
        favoriteAt: '2024-01-20',
        logo: '/companies/alibaba.png'
      },
      {
        id: '2',
        name: '字节跳动',
        industry: '互联网',
        location: '北京',
        size: '10000+',
        rating: 4.3,
        reviewCount: 1100,
        salaryLevel: '很高',
        tags: ['年轻化', '快速成长', '压力大'],
        favoriteAt: '2024-01-18',
        logo: '/companies/bytedance.png'
      }
    ],
    interviews: [
      {
        id: '1',
        title: '阿里巴巴前端工程师面试经验',
        company: '阿里巴巴',
        position: '前端工程师',
        result: 'offer',
        difficulty: 'medium',
        author: '匿名用户',
        likes: 156,
        comments: 23,
        tags: ['前端', '算法', '项目经验'],
        favoriteAt: '2024-01-19',
        submittedAt: '2024-01-15'
      },
      {
        id: '2',
        title: '腾讯后端开发岗位面试分享',
        company: '腾讯',
        position: '后端工程师',
        result: 'offer',
        difficulty: 'hard',
        author: '技术宅',
        likes: 98,
        comments: 15,
        tags: ['后端', '系统设计', '算法'],
        favoriteAt: '2024-01-17',
        submittedAt: '2024-01-10'
      }
    ],
    posts: [
      {
        id: '1',
        title: '如何在大厂中快速成长？分享我的3年经验',
        content: '在大厂工作3年，从初级工程师成长为技术专家，分享一些心得...',
        author: {
          name: '技术老司机',
          avatar: '/avatar1.jpg'
        },
        category: '职业发展',
        likes: 89,
        comments: 45,
        views: 2340,
        tags: ['职业发展', '大厂经验', '技术成长'],
        favoriteAt: '2024-01-16',
        createdAt: '2024-01-10'
      },
      {
        id: '2',
        title: '程序员如何做好技术规划？',
        content: '技术路线规划对程序员很重要，这里分享一些思路...',
        author: {
          name: '架构师小王',
          avatar: '/avatar2.jpg'
        },
        category: '技术分享',
        likes: 67,
        comments: 23,
        views: 1890,
        tags: ['技术规划', '职业规划', '架构'],
        favoriteAt: '2024-01-14',
        createdAt: '2024-01-08'
      }
    ],
    salaries: [
      {
        id: '1',
        position: '前端工程师',
        company: '阿里巴巴',
        location: '杭州',
        experience: '3-5年',
        baseSalary: 280000,
        totalSalary: 360000,
        level: 'P6',
        favoriteAt: '2024-01-15',
        submittedAt: '2024-01-10'
      },
      {
        id: '2',
        position: '算法工程师',
        company: '字节跳动',
        location: '北京',
        experience: '1-3年',
        baseSalary: 320000,
        totalSalary: 420000,
        level: '2-1',
        favoriteAt: '2024-01-12',
        submittedAt: '2024-01-05'
      }
    ]
  }

  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold mb-2">我的收藏</h1>
            <p className="text-gray-600">
              管理您收藏的企业、面经、帖子和薪资信息
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('grid')}
            >
              <Grid3X3 className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === 'list' ? 'default' : 'outline'}
              size="sm"
              onClick={() => setViewMode('list')}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {/* 搜索和筛选 */}
      <Card className="mb-6">
        <CardContent className="p-4">
          <div className="flex items-center justify-between gap-4">
            <div className="flex items-center gap-4 flex-1">
              <div className="relative flex-1 max-w-md">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input placeholder="搜索收藏内容..." className="pl-10" />
              </div>
              
              <Select defaultValue="all">
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="排序方式" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">最新收藏</SelectItem>
                  <SelectItem value="oldest">最早收藏</SelectItem>
                  <SelectItem value="popular">最受欢迎</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {selectedItems.length > 0 && (
              <Button variant="outline" size="sm" onClick={handleRemoveFavorites}>
                <Trash2 className="mr-2 h-4 w-4" />
                移除收藏 ({selectedItems.length})
              </Button>
            )}
          </div>
        </CardContent>
      </Card>

      {/* 收藏内容 */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList className="grid w-full max-w-2xl grid-cols-5">
          <TabsTrigger value="all">
            全部 ({Object.values(favoriteData).reduce((sum, items) => sum + items.length, 0)})
          </TabsTrigger>
          <TabsTrigger value="companies">
            企业 ({favoriteData.companies.length})
          </TabsTrigger>
          <TabsTrigger value="interviews">
            面经 ({favoriteData.interviews.length})
          </TabsTrigger>
          <TabsTrigger value="posts">
            帖子 ({favoriteData.posts.length})
          </TabsTrigger>
          <TabsTrigger value="salaries">
            薪资 ({favoriteData.salaries.length})
          </TabsTrigger>
        </TabsList>

        {/* 全部收藏 */}
        <TabsContent value="all" className="space-y-6">
          <div>
            <h3 className="text-lg font-semibold mb-4">企业</h3>
            <CompanyFavorites 
              companies={favoriteData.companies} 
              viewMode={viewMode}
              selectedItems={selectedItems}
              onSelectItem={handleSelectItem}
            />
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">面经</h3>
            <InterviewFavorites 
              interviews={favoriteData.interviews}
              viewMode={viewMode}
              selectedItems={selectedItems}
              onSelectItem={handleSelectItem}
            />
          </div>
          <div>
            <h3 className="text-lg font-semibold mb-4">帖子</h3>
            <PostFavorites 
              posts={favoriteData.posts}
              viewMode={viewMode}
              selectedItems={selectedItems}
              onSelectItem={handleSelectItem}
            />
          </div>
        </TabsContent>

        {/* 企业收藏 */}
        <TabsContent value="companies">
          <CompanyFavorites 
            companies={favoriteData.companies} 
            viewMode={viewMode}
            selectedItems={selectedItems}
            onSelectItem={handleSelectItem}
            showSelectAll
            onSelectAll={() => handleSelectAll(favoriteData.companies)}
          />
        </TabsContent>

        {/* 面经收藏 */}
        <TabsContent value="interviews">
          <InterviewFavorites 
            interviews={favoriteData.interviews}
            viewMode={viewMode}
            selectedItems={selectedItems}
            onSelectItem={handleSelectItem}
            showSelectAll
            onSelectAll={() => handleSelectAll(favoriteData.interviews)}
          />
        </TabsContent>

        {/* 帖子收藏 */}
        <TabsContent value="posts">
          <PostFavorites 
            posts={favoriteData.posts}
            viewMode={viewMode}
            selectedItems={selectedItems}
            onSelectItem={handleSelectItem}
            showSelectAll
            onSelectAll={() => handleSelectAll(favoriteData.posts)}
          />
        </TabsContent>

        {/* 薪资收藏 */}
        <TabsContent value="salaries">
          <SalaryFavorites 
            salaries={favoriteData.salaries}
            viewMode={viewMode}
            selectedItems={selectedItems}
            onSelectItem={handleSelectItem}
            showSelectAll
            onSelectAll={() => handleSelectAll(favoriteData.salaries)}
          />
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * 企业收藏组件
 */
function CompanyFavorites({ 
  companies, 
  viewMode, 
  selectedItems, 
  onSelectItem,
  showSelectAll = false,
  onSelectAll
}: {
  companies: any[]
  viewMode: 'grid' | 'list'
  selectedItems: string[]
  onSelectItem: (id: string) => void
  showSelectAll?: boolean
  onSelectAll?: () => void
}) {
  if (companies.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <Building2 className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无收藏企业</h3>
          <p className="text-gray-600 mb-4">收藏感兴趣的企业，方便随时查看</p>
          <Link href="/companies">
            <Button>浏览企业</Button>
          </Link>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {showSelectAll && (
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <input
            type="checkbox"
            checked={selectedItems.length === companies.length}
            onChange={onSelectAll}
            className="rounded"
          />
          <span>全选</span>
        </div>
      )}

      <div className={viewMode === 'grid' ? 'grid gap-4 md:grid-cols-2 lg:grid-cols-3' : 'space-y-4'}>
        {companies.map(company => (
          <Card key={company.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  checked={selectedItems.includes(company.id)}
                  onChange={() => onSelectItem(company.id)}
                  className="mt-1 rounded"
                />
                
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <Link href={`/companies/${company.id}`} className="hover:text-blue-600">
                        <h3 className="font-semibold">{company.name}</h3>
                      </Link>
                      <p className="text-sm text-gray-600">
                        {company.industry} • {company.size}人 • {company.location}
                      </p>
                    </div>
                    <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                      <span className="text-xs text-gray-400">Logo</span>
                    </div>
                  </div>

                  <div className="flex items-center gap-2 mb-3">
                    <div className="flex items-center">
                      <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      <span className="ml-1 font-medium">{company.rating}</span>
                    </div>
                    <span className="text-sm text-gray-600">
                      {company.reviewCount} 条评价
                    </span>
                    <Badge variant="secondary">薪资{company.salaryLevel}</Badge>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-3">
                    {company.tags.slice(0, 3).map((tag: string) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>收藏于 {company.favoriteAt}</span>
                    <Link href={`/companies/${company.id}`}>
                      <Button size="sm" variant="outline">
                        <ExternalLink className="mr-1 h-3 w-3" />
                        查看
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

/**
 * 面经收藏组件
 */
function InterviewFavorites({ 
  interviews, 
  viewMode, 
  selectedItems, 
  onSelectItem,
  showSelectAll = false,
  onSelectAll
}: {
  interviews: any[]
  viewMode: 'grid' | 'list'
  selectedItems: string[]
  onSelectItem: (id: string) => void
  showSelectAll?: boolean
  onSelectAll?: () => void
}) {
  if (interviews.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无收藏面经</h3>
          <p className="text-gray-600 mb-4">收藏有用的面经，为求职做准备</p>
          <Link href="/companies/interviews">
            <Button>浏览面经</Button>
          </Link>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {showSelectAll && (
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <input
            type="checkbox"
            checked={selectedItems.length === interviews.length}
            onChange={onSelectAll}
            className="rounded"
          />
          <span>全选</span>
        </div>
      )}

      <div className={viewMode === 'grid' ? 'grid gap-4 md:grid-cols-2' : 'space-y-4'}>
        {interviews.map(interview => (
          <Card key={interview.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  checked={selectedItems.includes(interview.id)}
                  onChange={() => onSelectItem(interview.id)}
                  className="mt-1 rounded"
                />
                
                <div className="flex-1">
                  <Link href={`/companies/interviews/${interview.id}`} className="hover:text-blue-600">
                    <h3 className="font-semibold mb-2">{interview.title}</h3>
                  </Link>
                  
                  <div className="flex items-center gap-2 mb-2 text-sm text-gray-600">
                    <Building2 className="h-3 w-3" />
                    <span>{interview.company}</span>
                    <span>•</span>
                    <span>{interview.position}</span>
                  </div>

                  <div className="flex items-center gap-2 mb-3">
                    <Badge 
                      variant={interview.result === 'offer' ? 'default' : 'destructive'}
                      className="text-xs"
                    >
                      {interview.result === 'offer' ? '获得Offer' : '被拒绝'}
                    </Badge>
                    <Badge variant="outline" className="text-xs">
                      难度: {interview.difficulty === 'medium' ? '中等' : '困难'}
                    </Badge>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-3">
                    {interview.tags.slice(0, 3).map((tag: string) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <ThumbsUp className="h-3 w-3" />
                        {interview.likes}
                      </span>
                      <span className="flex items-center gap-1">
                        <MessageSquare className="h-3 w-3" />
                        {interview.comments}
                      </span>
                    </div>
                    <span>收藏于 {interview.favoriteAt}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

/**
 * 帖子收藏组件
 */
function PostFavorites({ 
  posts, 
  viewMode, 
  selectedItems, 
  onSelectItem,
  showSelectAll = false,
  onSelectAll
}: {
  posts: any[]
  viewMode: 'grid' | 'list'
  selectedItems: string[]
  onSelectItem: (id: string) => void
  showSelectAll?: boolean
  onSelectAll?: () => void
}) {
  if (posts.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <MessageSquare className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无收藏帖子</h3>
          <p className="text-gray-600 mb-4">收藏有价值的讨论和分享</p>
          <Link href="/forum">
            <Button>浏览论坛</Button>
          </Link>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {showSelectAll && (
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <input
            type="checkbox"
            checked={selectedItems.length === posts.length}
            onChange={onSelectAll}
            className="rounded"
          />
          <span>全选</span>
        </div>
      )}

      <div className={viewMode === 'grid' ? 'grid gap-4 md:grid-cols-2 lg:grid-cols-3' : 'space-y-4'}>
        {posts.map(post => (
          <Card key={post.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  checked={selectedItems.includes(post.id)}
                  onChange={() => onSelectItem(post.id)}
                  className="mt-1 rounded"
                />
                
                <div className="flex-1">
                  <Link href={`/forum/${post.id}`} className="hover:text-blue-600">
                    <h3 className="font-semibold mb-2">{post.title}</h3>
                  </Link>
                  
                  <p className="text-gray-700 text-sm mb-3 line-clamp-2">
                    {post.content}
                  </p>

                  <div className="flex items-center gap-3 mb-3">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={post.author.avatar} />
                      <AvatarFallback>{post.author.name[0]}</AvatarFallback>
                    </Avatar>
                    <span className="text-sm text-gray-600">{post.author.name}</span>
                    <Badge variant="secondary" className="text-xs">
                      {post.category}
                    </Badge>
                  </div>

                  <div className="flex flex-wrap gap-1 mb-3">
                    {post.tags.slice(0, 3).map((tag: string) => (
                      <Badge key={tag} variant="outline" className="text-xs">
                        {tag}
                      </Badge>
                    ))}
                  </div>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <div className="flex items-center gap-4">
                      <span className="flex items-center gap-1">
                        <ThumbsUp className="h-3 w-3" />
                        {post.likes}
                      </span>
                      <span className="flex items-center gap-1">
                        <MessageSquare className="h-3 w-3" />
                        {post.comments}
                      </span>
                      <span>{post.views} 浏览</span>
                    </div>
                    <span>收藏于 {post.favoriteAt}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

/**
 * 薪资收藏组件
 */
function SalaryFavorites({ 
  salaries, 
  viewMode, 
  selectedItems, 
  onSelectItem,
  showSelectAll = false,
  onSelectAll
}: {
  salaries: any[]
  viewMode: 'grid' | 'list'
  selectedItems: string[]
  onSelectItem: (id: string) => void
  showSelectAll?: boolean
  onSelectAll?: () => void
}) {
  if (salaries.length === 0) {
    return (
      <Card>
        <CardContent className="p-12 text-center">
          <DollarSign className="mx-auto h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium text-gray-900 mb-2">暂无收藏薪资</h3>
          <p className="text-gray-600 mb-4">收藏有参考价值的薪资信息</p>
          <Link href="/companies/salaries">
            <Button>浏览薪资</Button>
          </Link>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-4">
      {showSelectAll && (
        <div className="flex items-center gap-2 text-sm text-gray-600">
          <input
            type="checkbox"
            checked={selectedItems.length === salaries.length}
            onChange={onSelectAll}
            className="rounded"
          />
          <span>全选</span>
        </div>
      )}

      <div className={viewMode === 'grid' ? 'grid gap-4 md:grid-cols-2 lg:grid-cols-3' : 'space-y-4'}>
        {salaries.map(salary => (
          <Card key={salary.id} className="hover:shadow-lg transition-shadow">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <input
                  type="checkbox"
                  checked={selectedItems.includes(salary.id)}
                  onChange={() => onSelectItem(salary.id)}
                  className="mt-1 rounded"
                />
                
                <div className="flex-1">
                  <div className="flex items-start justify-between mb-2">
                    <div>
                      <h3 className="font-semibold">{salary.position}</h3>
                      <div className="flex items-center gap-2 text-sm text-gray-600">
                        <Building2 className="h-3 w-3" />
                        <span>{salary.company}</span>
                        <span>•</span>
                        <MapPin className="h-3 w-3" />
                        <span>{salary.location}</span>
                      </div>
                    </div>
                    <div className="text-right">
                      <p className="font-bold text-green-600">
                        {(salary.totalSalary / 10000).toFixed(1)}万
                      </p>
                      <p className="text-xs text-gray-500">总包</p>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 mb-3 text-sm">
                    <div>
                      <span className="text-gray-600">基本年薪：</span>
                      <span className="font-medium">{(salary.baseSalary / 10000).toFixed(1)}万</span>
                    </div>
                    <div>
                      <span className="text-gray-600">工作经验：</span>
                      <span className="font-medium">{salary.experience}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">职级：</span>
                      <span className="font-medium">{salary.level}</span>
                    </div>
                    <div>
                      <span className="text-gray-600">提交时间：</span>
                      <span className="font-medium">{salary.submittedAt}</span>
                    </div>
                  </div>

                  <div className="flex items-center justify-between text-xs text-gray-500">
                    <span>收藏于 {salary.favoriteAt}</span>
                    <Link href="/companies/salaries">
                      <Button size="sm" variant="outline">
                        <ExternalLink className="mr-1 h-3 w-3" />
                        查看详情
                      </Button>
                    </Link>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
} 