import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Progress } from '@/components/ui/progress'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import {
    ArrowLeft,
    Briefcase,
    Calendar,
    Filter,
    MessageSquare,
    Plus,
    Star,
    ThumbsDown,
    ThumbsUp
} from 'lucide-react'
import Link from 'next/link'
import { notFound } from 'next/navigation'
import { Suspense } from 'react'

interface CompanyReviewsPageProps {
  params: {
    id: string
  }
}

/**
 * 企业评价页面
 * 展示企业的工作体验评价和风评
 */
export default function CompanyReviewsPage({ params }: CompanyReviewsPageProps) {
  const companyId = params.id

  return (
    <div className="container mx-auto px-4 py-8">
      <Suspense fallback={<ReviewsPageSkeleton />}>
        <CompanyReviewsContent companyId={companyId} />
      </Suspense>
    </div>
  )
}

/**
 * 企业评价内容组件
 */
function CompanyReviewsContent({ companyId }: { companyId: string }) {
  // 模拟数据获取
  const company = getCompanyById(companyId)
  
  if (!company) {
    notFound()
  }

  return (
    <div className="max-w-6xl mx-auto space-y-6">
      {/* 返回按钮 */}
      <Link href={`/companies/${companyId}`}>
        <Button variant="ghost" className="mb-4">
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回企业详情
        </Button>
      </Link>

      {/* 企业基本信息 */}
      <Card>
        <CardHeader>
          <div className="flex justify-between items-start">
            <div>
              <h1 className="text-3xl font-bold mb-2">{company.name}</h1>
              <p className="text-gray-600 mb-4">{company.industry} • {company.size}人 • {company.location}</p>
              
              {/* 评分概览 */}
              <div className="flex items-center gap-6">
                <div className="flex items-center gap-2">
                  <Star className="h-6 w-6 fill-yellow-400 text-yellow-400" />
                  <span className="text-2xl font-bold">{company.rating}</span>
                  <span className="text-gray-600">({company.reviewCount} 条评价)</span>
                </div>
                <Badge variant="secondary" className="text-sm">
                  {getRecommendationText(company.recommendation)}%推荐
                </Badge>
              </div>
            </div>
            
            <Link href={`/companies/${companyId}/reviews/submit`}>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                写评价
              </Button>
            </Link>
          </div>
        </CardHeader>
      </Card>

      {/* 评分详情 */}
      <Card>
        <CardHeader>
          <CardTitle>评分详情</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid gap-6 md:grid-cols-2">
            {/* 各维度评分 */}
            <div className="space-y-4">
              <h3 className="font-semibold mb-3">各维度评分</h3>
              {company.ratingDetails.map(item => (
                <div key={item.category} className="space-y-2">
                  <div className="flex justify-between items-center">
                    <span className="text-sm font-medium">{item.category}</span>
                    <span className="text-sm font-bold">{item.score}</span>
                  </div>
                  <Progress value={item.score * 20} className="h-2" />
                </div>
              ))}
            </div>

            {/* 评分分布 */}
            <div className="space-y-4">
              <h3 className="font-semibold mb-3">评分分布</h3>
              {company.ratingDistribution.map(item => (
                <div key={item.stars} className="flex items-center gap-3">
                  <div className="flex items-center gap-1 w-16">
                    <span className="text-sm">{item.stars}</span>
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                  </div>
                  <Progress value={item.percentage} className="flex-1 h-2" />
                  <span className="text-sm text-gray-600 w-12">
                    {item.count}人
                  </span>
                </div>
              ))}
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 筛选和排序 */}
      <Card>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <Select defaultValue="all">
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="所有评分" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有评分</SelectItem>
                  <SelectItem value="5">5星</SelectItem>
                  <SelectItem value="4">4星</SelectItem>
                  <SelectItem value="3">3星</SelectItem>
                  <SelectItem value="2">2星</SelectItem>
                  <SelectItem value="1">1星</SelectItem>
                </SelectContent>
              </Select>

              <Select defaultValue="all">
                <SelectTrigger className="w-32">
                  <SelectValue placeholder="所有职位" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">所有职位</SelectItem>
                  <SelectItem value="engineer">工程师</SelectItem>
                  <SelectItem value="pm">产品经理</SelectItem>
                  <SelectItem value="designer">设计师</SelectItem>
                  <SelectItem value="operations">运营</SelectItem>
                </SelectContent>
              </Select>

              <Button variant="outline" size="sm">
                <Filter className="mr-2 h-4 w-4" />
                更多筛选
              </Button>
            </div>

            <Select defaultValue="latest">
              <SelectTrigger className="w-32">
                <SelectValue placeholder="排序方式" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="latest">最新</SelectItem>
                <SelectItem value="helpful">最有用</SelectItem>
                <SelectItem value="rating-high">评分高</SelectItem>
                <SelectItem value="rating-low">评分低</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* 评价列表 */}
      <Tabs defaultValue="all" className="space-y-4">
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="all">所有评价</TabsTrigger>
          <TabsTrigger value="current">在职员工</TabsTrigger>
          <TabsTrigger value="former">前员工</TabsTrigger>
        </TabsList>

        <TabsContent value="all" className="space-y-4">
          <ReviewList reviews={company.reviews} />
        </TabsContent>

        <TabsContent value="current" className="space-y-4">
          <ReviewList reviews={company.reviews.filter(r => r.employmentStatus === 'current')} />
        </TabsContent>

        <TabsContent value="former" className="space-y-4">
          <ReviewList reviews={company.reviews.filter(r => r.employmentStatus === 'former')} />
        </TabsContent>
      </Tabs>

      {/* 分页 */}
      <div className="flex justify-center">
        <Button variant="outline">
          加载更多评价
        </Button>
      </div>
    </div>
  )
}

/**
 * 评价列表组件
 */
function ReviewList({ reviews }: { reviews: any[] }) {
  return (
    <div className="space-y-6">
      {reviews.map(review => (
        <Card key={review.id}>
          <CardContent className="p-6">
            {/* 评价头部 */}
            <div className="flex justify-between items-start mb-4">
              <div className="flex items-center gap-3">
                <Avatar>
                  <AvatarImage src={review.author.avatar} />
                  <AvatarFallback>{review.author.name[0]}</AvatarFallback>
                </Avatar>
                <div>
                  <div className="flex items-center gap-2">
                    <span className="font-medium">{review.author.name}</span>
                    <Badge variant="outline" className="text-xs">
                      {review.employmentStatus === 'current' ? '在职' : '前员工'}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-gray-600">
                    <Briefcase className="h-3 w-3" />
                    <span>{review.position}</span>
                    <span>•</span>
                    <span>{review.department}</span>
                  </div>
                </div>
              </div>

              <div className="text-right">
                <div className="flex items-center gap-1 mb-1">
                  <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                  <span className="font-bold">{review.rating}</span>
                </div>
                <div className="flex items-center gap-1 text-xs text-gray-500">
                  <Calendar className="h-3 w-3" />
                  <span>{review.submittedAt}</span>
                </div>
              </div>
            </div>

            {/* 评价标题 */}
            <h3 className="font-semibold mb-3">{review.title}</h3>

            {/* 各维度评分 */}
            <div className="grid gap-2 md:grid-cols-4 mb-4">
              {review.dimensions.map((dim: any) => (
                <div key={dim.name} className="flex items-center justify-between text-sm">
                  <span className="text-gray-600">{dim.name}</span>
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
                    <span className="font-medium">{dim.score}</span>
                  </div>
                </div>
              ))}
            </div>

            {/* 评价内容 */}
            <div className="space-y-3 mb-4">
              {review.pros && (
                <div>
                  <h4 className="font-medium text-green-700 mb-1">优点：</h4>
                  <p className="text-gray-700">{review.pros}</p>
                </div>
              )}
              {review.cons && (
                <div>
                  <h4 className="font-medium text-red-700 mb-1">缺点：</h4>
                  <p className="text-gray-700">{review.cons}</p>
                </div>
              )}
              {review.advice && (
                <div>
                  <h4 className="font-medium text-blue-700 mb-1">建议：</h4>
                  <p className="text-gray-700">{review.advice}</p>
                </div>
              )}
            </div>

            {/* 标签 */}
            {review.tags && review.tags.length > 0 && (
              <div className="flex flex-wrap gap-2 mb-4">
                {review.tags.map((tag: string) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            )}

            {/* 互动区域 */}
            <div className="flex items-center justify-between pt-4 border-t">
              <div className="flex items-center gap-4">
                <Button variant="ghost" size="sm">
                  <ThumbsUp className="mr-2 h-4 w-4" />
                  有用 ({review.helpful})
                </Button>
                <Button variant="ghost" size="sm">
                  <ThumbsDown className="mr-2 h-4 w-4" />
                  无用 ({review.unhelpful})
                </Button>
                <Button variant="ghost" size="sm">
                  <MessageSquare className="mr-2 h-4 w-4" />
                  回复 ({review.replies})
                </Button>
              </div>
              
              {review.verified && (
                <Badge variant="outline" className="text-xs text-green-600">
                  已验证
                </Badge>
              )}
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}

/**
 * 加载骨架屏
 */
function ReviewsPageSkeleton() {
  return (
    <div className="max-w-6xl mx-auto space-y-6">
      <div className="h-8 bg-gray-200 rounded animate-pulse" />
      <div className="space-y-4">
        <div className="h-32 bg-gray-200 rounded animate-pulse" />
        <div className="h-24 bg-gray-200 rounded animate-pulse" />
        <div className="h-48 bg-gray-200 rounded animate-pulse" />
        <div className="h-48 bg-gray-200 rounded animate-pulse" />
      </div>
    </div>
  )
}

/**
 * 模拟数据获取函数
 */
function getCompanyById(id: string) {
  const companies = {
    '1': {
      id: '1',
      name: '阿里巴巴',
      industry: '互联网',
      size: '10000+',
      location: '杭州',
      rating: 4.2,
      reviewCount: 1250,
      recommendation: 78,
      ratingDetails: [
        { category: '薪资水平', score: 4.5 },
        { category: '工作环境', score: 4.1 },
        { category: '管理制度', score: 3.8 },
        { category: '发展前景', score: 4.3 },
        { category: '工作生活平衡', score: 3.5 },
      ],
      ratingDistribution: [
        { stars: 5, count: 450, percentage: 36 },
        { stars: 4, count: 525, percentage: 42 },
        { stars: 3, count: 188, percentage: 15 },
        { stars: 2, count: 62, percentage: 5 },
        { stars: 1, count: 25, percentage: 2 },
      ],
      reviews: [
        {
          id: 1,
          author: {
            name: '匿名员工',
            avatar: '/avatar1.jpg',
          },
          position: '前端工程师',
          department: '技术部',
          employmentStatus: 'current',
          rating: 4.5,
          title: '总体来说是个不错的公司',
          dimensions: [
            { name: '薪资', score: 4.8 },
            { name: '环境', score: 4.2 },
            { name: '管理', score: 4.0 },
            { name: '发展', score: 4.5 },
          ],
          pros: '薪资待遇在行业内算是不错的，技术氛围比较好，能学到很多东西。同事之间关系融洽，团队协作效率高。公司平台大，接触的项目都比较有挑战性。',
          cons: '工作节奏比较快，压力相对较大。加班情况时有发生，特别是项目上线前。晋升竞争比较激烈，需要付出更多努力。',
          advice: '建议新人要有心理准备，工作强度会比较大。但是如果能坚持下来，收获还是很大的。要主动学习新技术，跟上公司的发展步伐。',
          tags: ['技术成长', '薪资不错', '加班多', '平台大'],
          helpful: 89,
          unhelpful: 5,
          replies: 12,
          verified: true,
          submittedAt: '2024-01-20',
        },
        {
          id: 2,
          author: {
            name: '前阿里员工',
            avatar: '/avatar2.jpg',
          },
          position: '产品经理',
          department: '产品部',
          employmentStatus: 'former',
          rating: 3.8,
          title: '适合快速成长，但要有压力承受能力',
          dimensions: [
            { name: '薪资', score: 4.5 },
            { name: '环境', score: 4.0 },
            { name: '管理', score: 3.5 },
            { name: '发展', score: 4.2 },
          ],
          pros: '业务发展迅速，能接触到很多前沿的产品理念。团队年轻有活力，学习氛围很好。薪资水平在市场上有竞争力。',
          cons: 'KPI压力比较大，经常需要加班赶项目进度。部门之间协调有时候效率不高。工作生活平衡做得不是特别好。',
          advice: '如果你想快速成长并且能承受一定压力，这里是不错的选择。但要做好长期加班的心理准备。',
          tags: ['成长快', 'KPI压力', '年轻团队'],
          helpful: 67,
          unhelpful: 8,
          replies: 8,
          verified: false,
          submittedAt: '2024-01-15',
        },
      ],
    }
  }

  return companies[id as keyof typeof companies] || null
}

/**
 * 获取推荐度文本
 */
function getRecommendationText(percentage: number): string {
  return percentage.toString()
} 