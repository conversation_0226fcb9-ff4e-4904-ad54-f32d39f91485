import { Suspense } from 'react'
import Link from 'next/link'
import { But<PERSON> } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { 
  Select, 
  SelectContent, 
  SelectItem, 
  SelectTrigger, 
  SelectValue 
} from '@/components/ui/select'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  Search, 
  Filter, 
  Plus, 
  Building2, 
  MapPin,
  ThumbsUp,
  MessageSquare,
  TrendingUp,
  Award
} from 'lucide-react'

/**
 * 面经分享页面
 * 展示面试经验分享和求职建议
 */
export default function InterviewsPage() {
  return (
    <div className="container mx-auto px-4 py-8">
      {/* 页面标题 */}
      <div className="mb-8">
        <h1 className="text-3xl font-bold mb-2">面经分享</h1>
        <p className="text-gray-600">真实面试经验分享，助你求职成功</p>
      </div>

      {/* 搜索和筛选栏 */}
      <Card className="mb-8">
        <CardContent className="p-6">
          <div className="grid gap-4 md:grid-cols-5">
            {/* 公司搜索 */}
            <div className="md:col-span-2">
              <div className="relative">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input placeholder="搜索公司..." className="pl-10" />
              </div>
            </div>

            {/* 职位搜索 */}
            <div>
              <Input placeholder="职位名称" />
            </div>

            {/* 面试结果筛选 */}
            <div>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="面试结果" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="offer">获得Offer</SelectItem>
                  <SelectItem value="reject">被拒绝</SelectItem>
                  <SelectItem value="pending">流程中</SelectItem>
                  <SelectItem value="withdraw">主动放弃</SelectItem>
                </SelectContent>
              </Select>
            </div>

            {/* 面试难度筛选 */}
            <div>
              <Select>
                <SelectTrigger>
                  <SelectValue placeholder="面试难度" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="easy">简单</SelectItem>
                  <SelectItem value="medium">中等</SelectItem>
                  <SelectItem value="hard">困难</SelectItem>
                  <SelectItem value="very-hard">很困难</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          <div className="flex justify-between items-center mt-4">
            <Button variant="outline" size="sm">
              <Filter className="mr-2 h-4 w-4" />
              更多筛选
            </Button>
            <div className="flex items-center gap-4">
              <span className="text-sm text-gray-600">
                找到 <span className="font-semibold">1,892</span> 条面经
              </span>
              <Button>
                <Plus className="mr-2 h-4 w-4" />
                分享面经
              </Button>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* 内容标签页 */}
      <Tabs defaultValue="latest" className="space-y-4">
        <TabsList className="grid w-full max-w-md grid-cols-3">
          <TabsTrigger value="latest">最新面经</TabsTrigger>
          <TabsTrigger value="popular">热门面经</TabsTrigger>
          <TabsTrigger value="tips">面试技巧</TabsTrigger>
        </TabsList>

        {/* 最新面经 */}
        <TabsContent value="latest" className="space-y-4">
          <Suspense fallback={<InterviewListSkeleton />}>
            <InterviewList type="latest" />
          </Suspense>
        </TabsContent>

        {/* 热门面经 */}
        <TabsContent value="popular" className="space-y-4">
          <Suspense fallback={<InterviewListSkeleton />}>
            <InterviewList type="popular" />
          </Suspense>
        </TabsContent>

        {/* 面试技巧 */}
        <TabsContent value="tips" className="space-y-4">
          <InterviewTips />
        </TabsContent>
      </Tabs>
    </div>
  )
}

/**
 * 面经列表组件
 */
function InterviewList({ type }: { type: 'latest' | 'popular' }) {
  // 模拟面经数据
  const interviews = [
    {
      id: 1,
      title: '阿里巴巴前端工程师面试经验',
      company: '阿里巴巴',
      position: '前端工程师',
      location: '杭州',
      result: 'offer',
      difficulty: 'medium',
      author: {
        id: 1,
        name: '匿名用户',
        avatar: '/avatar1.jpg',
        experience: '3年经验',
      },
      content: '一共四轮面试，包括技术面试、项目面试、HR面试等。整体感觉还是比较有挑战性的...',
      tags: ['前端', '算法', '项目经验'],
      likes: 156,
      comments: 23,
      views: 2340,
      createdAt: '2024-01-20',
      rounds: 4,
      processTime: '2周',
    },
    {
      id: 2,
      title: '腾讯后端开发岗位面试分享',
      company: '腾讯',
      position: '后端工程师',
      location: '深圳',
      result: 'offer',
      difficulty: 'hard',
      author: {
        id: 2,
        name: '技术宅',
        avatar: '/avatar2.jpg',
        experience: '5年经验',
      },
      content: '腾讯的面试确实很有挑战性，特别是算法题和系统设计部分。整个流程比较严格...',
      tags: ['后端', '系统设计', '算法'],
      likes: 98,
      comments: 15,
      views: 1580,
      createdAt: '2024-01-18',
      rounds: 5,
      processTime: '3周',
    },
    {
      id: 3,
      title: '字节跳动产品经理面试经历',
      company: '字节跳动',
      position: '产品经理',
      location: '北京',
      result: 'reject',
      difficulty: 'medium',
      author: {
        id: 3,
        name: '产品新人',
        avatar: '/avatar3.jpg',
        experience: '2年经验',
      },
      content: '虽然最终没有通过，但整个面试过程学到了很多。面试官很专业，问题也很有针对性...',
      tags: ['产品', '用户体验', '数据分析'],
      likes: 67,
      comments: 12,
      views: 980,
      createdAt: '2024-01-15',
      rounds: 3,
      processTime: '10天',
    },
  ]

  // 根据类型筛选数据
  const filteredInterviews = type === 'popular' 
    ? interviews.sort((a, b) => b.likes - a.likes)
    : interviews

  return (
    <div className="space-y-4">
      {filteredInterviews.map(interview => (
        <Card key={interview.id} className="hover:shadow-md transition-shadow">
          <CardHeader>
            <div className="flex items-start justify-between">
              <div className="flex-1">
                <div className="flex items-center gap-2 mb-2">
                  <CardTitle className="text-lg hover:text-primary cursor-pointer">
                    <Link href={`/companies/interviews/${interview.id}`}>
                      {interview.title}
                    </Link>
                  </CardTitle>
                  <Badge 
                    variant={
                      interview.result === 'offer' ? 'default' :
                      interview.result === 'reject' ? 'destructive' :
                      'secondary'
                    }
                  >
                    {
                      interview.result === 'offer' ? '获得Offer' :
                      interview.result === 'reject' ? '被拒绝' :
                      interview.result === 'pending' ? '流程中' :
                      '主动放弃'
                    }
                  </Badge>
                  <Badge variant="outline">
                    {
                      interview.difficulty === 'easy' ? '简单' :
                      interview.difficulty === 'medium' ? '中等' :
                      interview.difficulty === 'hard' ? '困难' :
                      '很困难'
                    }
                  </Badge>
                </div>

                <div className="flex items-center gap-4 text-sm text-gray-600 mb-3">
                  <span className="flex items-center gap-1">
                    <Building2 className="h-3 w-3" />
                    {interview.company}
                  </span>
                  <span>{interview.position}</span>
                  <span className="flex items-center gap-1">
                    <MapPin className="h-3 w-3" />
                    {interview.location}
                  </span>
                  <span>{interview.rounds}轮面试</span>
                  <span>历时{interview.processTime}</span>
                </div>

                <p className="text-gray-600 mb-3 line-clamp-2">{interview.content}</p>

                <div className="flex items-center gap-2 mb-3">
                  {interview.tags.map(tag => (
                    <Badge key={tag} variant="outline" className="text-xs">
                      {tag}
                    </Badge>
                  ))}
                </div>

                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-3">
                    <Avatar className="h-6 w-6">
                      <AvatarImage src={interview.author.avatar} alt={interview.author.name} />
                      <AvatarFallback>{interview.author.name[0]}</AvatarFallback>
                    </Avatar>
                    <span className="text-sm text-gray-600">
                      {interview.author.name} • {interview.author.experience}
                    </span>
                    <span className="text-sm text-gray-500">{interview.createdAt}</span>
                  </div>

                  <div className="flex items-center gap-4 text-sm text-gray-600">
                    <span className="flex items-center gap-1">
                      <ThumbsUp className="h-3 w-3" />
                      {interview.likes}
                    </span>
                    <span className="flex items-center gap-1">
                      <MessageSquare className="h-3 w-3" />
                      {interview.comments}
                    </span>
                  </div>
                </div>
              </div>
            </div>
          </CardHeader>
        </Card>
      ))}
    </div>
  )
}

/**
 * 面试技巧组件
 */
function InterviewTips() {
  const tips = [
    {
      id: 1,
      title: '技术面试准备指南',
      description: '如何准备算法题、系统设计和项目经验分享',
      icon: '💻',
      category: '技术面试',
      readTime: '5分钟',
    },
    {
      id: 2,
      title: 'HR面试常见问题解答',
      description: '薪资谈判、职业规划等HR面试技巧',
      icon: '🤝',
      category: 'HR面试',
      readTime: '3分钟',
    },
    {
      id: 3,
      title: '简历优化技巧',
      description: '如何写出让HR眼前一亮的简历',
      icon: '📄',
      category: '简历制作',
      readTime: '4分钟',
    },
    {
      id: 4,
      title: '线上面试注意事项',
      description: '视频面试的设备准备和表现技巧',
      icon: '📹',
      category: '面试技巧',
      readTime: '2分钟',
    },
  ]

  const statistics = [
    {
      title: '成功率最高的公司',
      items: [
        { name: '阿里巴巴', rate: '68%' },
        { name: '腾讯', rate: '65%' },
        { name: '美团', rate: '62%' },
        { name: '字节跳动', rate: '58%' },
      ]
    },
    {
      title: '最受欢迎的职位',
      items: [
        { name: '前端工程师', count: '523条' },
        { name: '后端工程师', count: '467条' },
        { name: '产品经理', count: '298条' },
        { name: '算法工程师', count: '234条' },
      ]
    }
  ]

  return (
    <div className="space-y-6">
      {/* 面试技巧卡片 */}
      <div className="grid gap-4 md:grid-cols-2">
        {tips.map(tip => (
          <Card key={tip.id} className="hover:shadow-md transition-shadow cursor-pointer">
            <CardContent className="p-6">
              <div className="flex items-start gap-4">
                <div className="text-3xl">{tip.icon}</div>
                <div className="flex-1">
                  <h3 className="font-semibold mb-2">{tip.title}</h3>
                  <p className="text-sm text-gray-600 mb-3">{tip.description}</p>
                  <div className="flex items-center justify-between">
                    <Badge variant="outline">{tip.category}</Badge>
                    <span className="text-xs text-gray-500">{tip.readTime}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* 统计数据 */}
      <div className="grid gap-6 md:grid-cols-2">
        {statistics.map((stat, index) => (
          <Card key={index}>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                {index === 0 ? <TrendingUp className="h-5 w-5" /> : <Award className="h-5 w-5" />}
                {stat.title}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {stat.items.map((item, itemIndex) => (
                  <div key={itemIndex} className="flex justify-between items-center">
                    <span className="text-sm">{item.name}</span>
                    <span className="font-semibold text-primary">
                      {(item as any).rate || (item as any).count}
                    </span>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  )
}

/**
 * 面经列表加载骨架屏
 */
function InterviewListSkeleton() {
  return (
    <div className="space-y-4">
      {Array.from({ length: 3 }).map((_, i) => (
        <Card key={i}>
          <CardHeader>
            <div className="animate-pulse">
              <div className="h-4 bg-gray-200 rounded w-3/4 mb-3"></div>
              <div className="h-3 bg-gray-200 rounded w-1/2 mb-3"></div>
              <div className="h-3 bg-gray-200 rounded w-full mb-3"></div>
              <div className="flex gap-2 mb-3">
                <div className="h-5 bg-gray-200 rounded w-16"></div>
                <div className="h-5 bg-gray-200 rounded w-16"></div>
                <div className="h-5 bg-gray-200 rounded w-16"></div>
              </div>
              <div className="flex justify-between">
                <div className="h-3 bg-gray-200 rounded w-32"></div>
                <div className="h-3 bg-gray-200 rounded w-20"></div>
              </div>
            </div>
          </CardHeader>
        </Card>
      ))}
    </div>
  )
} 