'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Checkbox } from '@/components/ui/checkbox'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import {
    Select,
    SelectContent,
    SelectItem,
    SelectTrigger,
    SelectValue
} from '@/components/ui/select'
import { Textarea } from '@/components/ui/textarea'
import {
    ArrowLeft,
    Building2,
    DollarSign,
    Info,
    Shield,
    User
} from 'lucide-react'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

/**
 * 薪资数据提交页面
 * 允许用户匿名提交薪资信息
 */
export default function SalarySubmitPage() {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [formData, setFormData] = useState({
    // 基本信息
    company: '',
    position: '',
    level: '',
    department: '',
    location: '',
    workYears: '',
    companyYears: '',
    
    // 薪资信息
    baseSalary: '',
    bonus: '',
    stockValue: '',
    allowances: '',
    otherBenefits: '',
    
    // 工作详情
    workHours: '',
    workIntensity: '',
    benefits: [] as string[],
    
    // 其他信息
    description: '',
    isAnonymous: true,
    agreeTerms: false,
  })

  const handleInputChange = (field: string, value: string | boolean | string[]) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }))
  }

  const handleBenefitChange = (benefit: string, checked: boolean) => {
    setFormData(prev => ({
      ...prev,
      benefits: checked
        ? [...prev.benefits, benefit]
        : prev.benefits.filter(b => b !== benefit)
    }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!formData.agreeTerms) {
      alert('请同意数据使用条款')
      return
    }

    setIsSubmitting(true)
    try {
      // TODO: 实际提交到API
      await new Promise(resolve => setTimeout(resolve, 2000))
      alert('薪资信息提交成功！感谢您的贡献')
      router.push('/companies/salaries')
    } catch (error) {
      console.error('提交薪资数据失败:', error)
      alert('提交失败，请稍后重试')
    } finally {
      setIsSubmitting(false)
    }
  }

  const benefitOptions = [
    '五险一金',
    '补充医疗保险',
    '年终奖',
    '季度奖金',
    '项目奖金',
    '股票期权',
    '免费餐食',
    '交通补助',
    '住房补助',
    '培训机会',
    '弹性工作',
    '带薪年假',
    '节日福利',
    '团建活动',
    '健身房',
    '其他'
  ]

  return (
    <div className="container mx-auto px-4 py-8 max-w-4xl">
      {/* 页面标题 */}
      <div className="mb-8">
        <Button
          variant="ghost"
          onClick={() => router.back()}
          className="mb-4"
        >
          <ArrowLeft className="mr-2 h-4 w-4" />
          返回
        </Button>
        <h1 className="text-3xl font-bold mb-2">贡献薪资数据</h1>
        <p className="text-gray-600">
          分享您的薪资信息，帮助其他求职者了解市场行情
        </p>
      </div>

      {/* 匿名说明 */}
      <Alert className="mb-8">
        <Shield className="h-4 w-4" />
        <AlertDescription>
          我们承诺保护您的隐私。所有薪资数据都将匿名处理，不会泄露您的个人信息。
        </AlertDescription>
      </Alert>

      <form onSubmit={handleSubmit} className="space-y-8">
        {/* 基本信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building2 className="h-5 w-5" />
              基本信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="company">公司名称 *</Label>
                <Input
                  id="company"
                  placeholder="请输入公司名称"
                  value={formData.company}
                  onChange={(e) => handleInputChange('company', e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="position">职位名称 *</Label>
                <Input
                  id="position"
                  placeholder="如：前端工程师"
                  value={formData.position}
                  onChange={(e) => handleInputChange('position', e.target.value)}
                  required
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-3">
              <div className="space-y-2">
                <Label htmlFor="level">职级</Label>
                <Input
                  id="level"
                  placeholder="如：P6, T3-2, L6"
                  value={formData.level}
                  onChange={(e) => handleInputChange('level', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="department">部门</Label>
                <Input
                  id="department"
                  placeholder="如：技术部"
                  value={formData.department}
                  onChange={(e) => handleInputChange('department', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="location">工作地点 *</Label>
                <Select value={formData.location} onValueChange={(value) => handleInputChange('location', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择城市" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="beijing">北京</SelectItem>
                    <SelectItem value="shanghai">上海</SelectItem>
                    <SelectItem value="guangzhou">广州</SelectItem>
                    <SelectItem value="shenzhen">深圳</SelectItem>
                    <SelectItem value="hangzhou">杭州</SelectItem>
                    <SelectItem value="chengdu">成都</SelectItem>
                    <SelectItem value="wuhan">武汉</SelectItem>
                    <SelectItem value="nanjing">南京</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="workYears">工作经验 *</Label>
                <Select value={formData.workYears} onValueChange={(value) => handleInputChange('workYears', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择工作年限" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0-1">0-1年</SelectItem>
                    <SelectItem value="1-3">1-3年</SelectItem>
                    <SelectItem value="3-5">3-5年</SelectItem>
                    <SelectItem value="5-10">5-10年</SelectItem>
                    <SelectItem value="10+">10年以上</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="companyYears">在该公司工作时间</Label>
                <Select value={formData.companyYears} onValueChange={(value) => handleInputChange('companyYears', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择在职时间" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="0-6m">0-6个月</SelectItem>
                    <SelectItem value="6m-1y">6个月-1年</SelectItem>
                    <SelectItem value="1-2y">1-2年</SelectItem>
                    <SelectItem value="2-5y">2-5年</SelectItem>
                    <SelectItem value="5y+">5年以上</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 薪资信息 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <DollarSign className="h-5 w-5" />
              薪资信息
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="baseSalary">基本年薪 * (元)</Label>
                <Input
                  id="baseSalary"
                  type="number"
                  placeholder="如：300000"
                  value={formData.baseSalary}
                  onChange={(e) => handleInputChange('baseSalary', e.target.value)}
                  required
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="bonus">年终奖金 (元)</Label>
                <Input
                  id="bonus"
                  type="number"
                  placeholder="如：50000"
                  value={formData.bonus}
                  onChange={(e) => handleInputChange('bonus', e.target.value)}
                />
              </div>
            </div>

            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="stockValue">股票期权价值 (元/年)</Label>
                <Input
                  id="stockValue"
                  type="number"
                  placeholder="如：30000"
                  value={formData.stockValue}
                  onChange={(e) => handleInputChange('stockValue', e.target.value)}
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="allowances">各类补助 (元/年)</Label>
                <Input
                  id="allowances"
                  type="number"
                  placeholder="如：12000"
                  value={formData.allowances}
                  onChange={(e) => handleInputChange('allowances', e.target.value)}
                />
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="otherBenefits">其他收入说明</Label>
              <Textarea
                id="otherBenefits"
                placeholder="如：项目奖金、加班费、其他福利等"
                value={formData.otherBenefits}
                onChange={(e) => handleInputChange('otherBenefits', e.target.value)}
                rows={3}
              />
            </div>
          </CardContent>
        </Card>

        {/* 工作详情 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <User className="h-5 w-5" />
              工作详情
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid gap-4 md:grid-cols-2">
              <div className="space-y-2">
                <Label htmlFor="workHours">平均工作时长</Label>
                <Select value={formData.workHours} onValueChange={(value) => handleInputChange('workHours', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择工作时长" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="8h">8小时/天</SelectItem>
                    <SelectItem value="9h">9小时/天</SelectItem>
                    <SelectItem value="10h">10小时/天</SelectItem>
                    <SelectItem value="11h">11小时/天</SelectItem>
                    <SelectItem value="12h+">12小时以上/天</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="workIntensity">工作强度</Label>
                <Select value={formData.workIntensity} onValueChange={(value) => handleInputChange('workIntensity', value)}>
                  <SelectTrigger>
                    <SelectValue placeholder="选择工作强度" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="low">轻松</SelectItem>
                    <SelectItem value="medium">适中</SelectItem>
                    <SelectItem value="high">较高</SelectItem>
                    <SelectItem value="very-high">很高</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-3">
              <Label>福利待遇 (可多选)</Label>
              <div className="grid gap-2 md:grid-cols-4">
                {benefitOptions.map(benefit => (
                  <div key={benefit} className="flex items-center space-x-2">
                    <Checkbox
                      id={benefit}
                      checked={formData.benefits.includes(benefit)}
                      onCheckedChange={(checked) => handleBenefitChange(benefit, checked as boolean)}
                    />
                    <Label htmlFor={benefit} className="text-sm">
                      {benefit}
                    </Label>
                  </div>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 补充说明 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Info className="h-5 w-5" />
              补充说明
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="description">详细说明</Label>
              <Textarea
                id="description"
                placeholder="请分享更多关于薪资、福利、工作环境等方面的信息..."
                value={formData.description}
                onChange={(e) => handleInputChange('description', e.target.value)}
                rows={4}
              />
            </div>

            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="isAnonymous"
                  checked={formData.isAnonymous}
                  onCheckedChange={(checked) => handleInputChange('isAnonymous', checked)}
                />
                <Label htmlFor="isAnonymous">
                  匿名提交（推荐）
                </Label>
              </div>

              <div className="flex items-center space-x-2">
                <Checkbox
                  id="agreeTerms"
                  checked={formData.agreeTerms}
                  onCheckedChange={(checked) => handleInputChange('agreeTerms', checked)}
                />
                <Label htmlFor="agreeTerms" className="text-sm">
                  我同意数据使用条款，了解提交的信息将用于平台统计分析
                </Label>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 提交按钮 */}
        <div className="flex justify-end space-x-4">
          <Button
            type="button"
            variant="outline"
            onClick={() => router.back()}
            disabled={isSubmitting}
          >
            取消
          </Button>
          <Button
            type="submit"
            disabled={isSubmitting || !formData.agreeTerms}
          >
            {isSubmitting ? '提交中...' : '提交薪资信息'}
          </Button>
        </div>
      </form>
    </div>
  )
} 