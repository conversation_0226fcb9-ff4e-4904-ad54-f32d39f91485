'use client'

import { Alert, AlertDescription } from '@/components/ui/alert'
import { Button } from '@/components/ui/button'
import {
    Card,
    CardContent,
    CardDescription,
    CardFooter,
    CardHeader,
    CardTitle,
} from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Chrome, Eye, EyeOff, Lock, Mail } from 'lucide-react'
import { signIn } from 'next-auth/react'
import Link from 'next/link'
import { useRouter } from 'next/navigation'
import { useState } from 'react'

/**
 * 登录页面
 * 支持邮箱密码登录和 Google OAuth 登录
 * 使用 NextAuth.js 进行身份认证管理
 * 
 * 功能特性：
 * - 表单验证和错误处理
 * - Google OAuth 一键登录
 * - 密码可见性切换
 * - 响应式设计，适配移动端
 * - 清晰的用户反馈机制
 */
export default function LoginPage() {
  const router = useRouter()
  const [showPassword, setShowPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)
  const [error, setError] = useState('')

  // 表单数据状态管理
  const [formData, setFormData] = useState({
    email: '',
    password: '',
  })

  /**
   * 处理邮箱密码登录
   * 使用NextAuth.js的credentials provider
   */
  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.email || !formData.password) {
      setError('请填写完整的登录信息')
      return
    }

    setIsLoading(true)
    setError('')

    try {
      // 使用 NextAuth.js signIn 方法
      const result = await signIn('credentials', {
        email: formData.email,
        password: formData.password,
        redirect: false,
      })
      
      if (result?.error) {
        setError('邮箱或密码错误，请重试')
      } else if (result?.ok) {
        // 登录成功，重定向到首页
        router.push('/')
        router.refresh()
      }
    } catch (error) {
      console.error('登录失败:', error)
      setError('登录失败，请稍后重试')
    } finally {
      setIsLoading(false)
    }
  }

  /**
   * 处理Google OAuth登录
   * 使用NextAuth.js的Google provider
   */
  const handleGoogleLogin = async () => {
    setIsGoogleLoading(true)
    setError('')

    try {
      // 使用 NextAuth.js signIn 方法，会自动重定向到Google
      await signIn('google', { callbackUrl: '/' })
    } catch (error) {
      console.error('Google登录失败:', error)
      setError('Google登录失败，请稍后重试')
      setIsGoogleLoading(false)
    }
  }

  /**
   * 处理输入变化
   */
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }))
  }

  return (
    <div className="min-h-screen flex items-center justify-center bg-gradient-to-br from-blue-50 to-purple-50 py-12 px-4 sm:px-6 lg:px-8">
      <div className="max-w-md w-full space-y-8">
        {/* Logo 和标题 */}
        <div className="text-center">
          <h1 className="text-3xl font-bold text-gray-900">WorkMates</h1>
          <p className="mt-2 text-gray-600">欢迎回到职场社区</p>
        </div>

        {/* 登录表单卡片 */}
        <Card className="shadow-lg border-0">
          <CardHeader className="space-y-1 pb-6">
            <CardTitle className="text-2xl font-semibold">登录账户</CardTitle>
            <CardDescription className="text-gray-600">
              选择登录方式继续使用 WorkMates
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            {/* 错误提示 */}
            {error && (
              <Alert variant="destructive">
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {/* Google 登录按钮 */}
            <Button 
              onClick={handleGoogleLogin}
              variant="outline" 
              className="w-full h-12 text-gray-700 border-gray-300 hover:bg-gray-50"
              disabled={isGoogleLoading || isLoading}
            >
              <Chrome className="mr-3 h-5 w-5 text-blue-500" />
              {isGoogleLoading ? '登录中...' : '使用 Google 登录'}
            </Button>

            {/* 分隔线 */}
            <div className="relative">
              <div className="absolute inset-0 flex items-center">
                <span className="w-full border-t border-gray-300" />
              </div>
              <div className="relative flex justify-center text-sm">
                <span className="bg-white px-4 text-gray-500">或使用邮箱登录</span>
              </div>
            </div>

            {/* 邮箱登录表单 */}
            <form onSubmit={handleSubmit} className="space-y-4">
              {/* 邮箱输入 */}
              <div className="space-y-2">
                <Label htmlFor="email" className="text-gray-700 font-medium">邮箱地址</Label>
                <div className="relative">
                  <Mail className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="email"
                    name="email"
                    type="email"
                    placeholder="<EMAIL>"
                    className="pl-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    value={formData.email}
                    onChange={handleInputChange}
                    required
                  />
                </div>
              </div>

              {/* 密码输入 */}
              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="password" className="text-gray-700 font-medium">密码</Label>
                  <Link
                    href="/auth/forgot-password"
                    className="text-sm text-blue-600 hover:text-blue-800 hover:underline"
                  >
                    忘记密码？
                  </Link>
                </div>
                <div className="relative">
                  <Lock className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    id="password"
                    name="password"
                    type={showPassword ? 'text' : 'password'}
                    placeholder="请输入密码"
                    className="pl-10 pr-10 h-12 border-gray-300 focus:border-blue-500 focus:ring-blue-500"
                    value={formData.password}
                    onChange={handleInputChange}
                    required
                  />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400 hover:text-gray-600 transition-colors"
                  >
                    {showPassword ? (
                      <EyeOff className="h-4 w-4" />
                    ) : (
                      <Eye className="h-4 w-4" />
                    )}
                  </button>
                </div>
              </div>

              {/* 记住我选项 */}
              <div className="flex items-center">
                <input
                  id="remember"
                  name="remember"
                  type="checkbox"
                  className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                />
                <Label
                  htmlFor="remember"
                  className="ml-3 text-sm text-gray-700 cursor-pointer"
                >
                  保持登录状态
                </Label>
              </div>

              {/* 登录按钮 */}
              <Button 
                type="submit" 
                className="w-full h-12 bg-blue-600 hover:bg-blue-700 text-white font-medium" 
                disabled={isLoading || isGoogleLoading}
              >
                {isLoading ? '登录中...' : '登录'}
              </Button>
            </form>
          </CardContent>
          <CardFooter className="flex flex-col space-y-4 pt-6">
            {/* 注册链接 */}
            <p className="text-center text-sm text-gray-600">
              还没有账户？
              <Link
                href="/auth/register"
                className="text-blue-600 hover:text-blue-800 hover:underline ml-1 font-medium"
              >
                立即注册
              </Link>
            </p>
          </CardFooter>
        </Card>

        {/* 附加信息 */}
        <div className="text-center">
          <p className="text-xs text-gray-500">
            继续使用即表示您同意我们的
            <Link href="/terms" className="text-blue-600 hover:underline mx-1">
              服务条款
            </Link>
            和
            <Link href="/privacy" className="text-blue-600 hover:underline mx-1">
              隐私政策
            </Link>
          </p>
        </div>
      </div>
    </div>
  )
}
