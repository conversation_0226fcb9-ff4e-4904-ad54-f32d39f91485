import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import crypto from 'crypto'

// 支持的文件类型配置
const FILE_CONFIGS = {
  avatar: {
    allowedTypes: ['image/jpeg', 'image/png', 'image/webp', 'image/gif'],
    maxSize: 5 * 1024 * 1024, // 5MB
    directory: 'avatars',
    description: '用户头像',
  },
  workFile: {
    allowedTypes: [
      'image/jpeg',
      'image/png',
      'image/webp',
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    ],
    maxSize: 10 * 1024 * 1024, // 10MB
    directory: 'work-files',
    description: '工作证明文件',
  },
  document: {
    allowedTypes: [
      'application/pdf',
      'application/msword',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'text/plain',
    ],
    maxSize: 20 * 1024 * 1024, // 20MB
    directory: 'documents',
    description: '文档文件',
  },
}

/**
 * 文件上传API
 * POST /api/upload
 *
 * Body (FormData):
 * - file: File - 要上传的文件
 * - type: string - 文件类型 (avatar, workFile, document)
 * - description?: string - 文件描述
 * - relatedId?: string - 关联ID（如工作经历ID）
 * - relatedType?: string - 关联类型（如 workExperience）
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再上传文件',
          },
        },
        { status: 401 }
      )
    }

    // 解析 FormData
    const formData = await request.formData()
    const file = formData.get('file') as File
    const type = formData.get('type') as string
    const description = (formData.get('description') as string) || ''
    const relatedId = (formData.get('relatedId') as string) || null
    const relatedType = (formData.get('relatedType') as string) || null

    // 验证必需参数
    if (!file || !type) {
      return NextResponse.json(
        {
          success: false,
          message: '缺少必需参数',
          error: {
            code: 'MISSING_PARAMETERS',
            message: '请提供文件和文件类型',
          },
        },
        { status: 400 }
      )
    }

    // 验证文件类型配置
    const config = FILE_CONFIGS[type as keyof typeof FILE_CONFIGS]
    if (!config) {
      return NextResponse.json(
        {
          success: false,
          message: '不支持的文件类型',
          error: {
            code: 'UNSUPPORTED_FILE_TYPE',
            message: `支持的类型: ${Object.keys(FILE_CONFIGS).join(', ')}`,
          },
        },
        { status: 400 }
      )
    }

    // 验证文件
    const validationResult = await validateFile(file, config)
    if (!validationResult.valid) {
      return NextResponse.json(
        {
          success: false,
          message: '文件验证失败',
          error: {
            code: 'FILE_VALIDATION_ERROR',
            message: validationResult.error,
          },
        },
        { status: 400 }
      )
    }

    // 生成文件名和路径
    const fileExtension = path.extname(file.name)
    const fileName = `${crypto.randomUUID()}${fileExtension}`
    const uploadDir = path.join(
      process.cwd(),
      'public',
      'uploads',
      config.directory
    )
    const filePath = path.join(uploadDir, fileName)
    const publicUrl = `/uploads/${config.directory}/${fileName}`

    // 确保上传目录存在
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // 保存文件到磁盘
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)
    await writeFile(filePath, buffer)

    // 计算文件哈希值（用于去重检测）
    const fileHash = crypto.createHash('sha256').update(buffer).digest('hex')

    // 保存文件信息到数据库
    const uploadedFile = await prisma.file.create({
      data: {
        originalName: file.name,
        fileName,
        filePath: publicUrl,
        fileSize: file.size,
        mimeType: file.type,
        fileHash,
        type,
        description,
        relatedId,
        relatedType,
        uploadedById: session.user.id,
      },
      select: {
        id: true,
        originalName: true,
        fileName: true,
        filePath: true,
        fileSize: true,
        mimeType: true,
        type: true,
        description: true,
        relatedId: true,
        relatedType: true,
        createdAt: true,
      },
    })

    // 如果是头像上传，更新用户头像
    if (type === 'avatar') {
      await prisma.user.update({
        where: { id: session.user.id },
        data: { avatar: publicUrl },
      })
    }

    // 更新用户积分（上传文件奖励）
    await updateUserPoints(session.user.id, 'FILE_UPLOADED', 5)

    return NextResponse.json(
      {
        success: true,
        message: '文件上传成功',
        data: uploadedFile,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('文件上传失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '文件上传失败',
        error: {
          code: 'UPLOAD_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 获取用户上传的文件列表
 * GET /api/upload
 *
 * 查询参数:
 * - type: 文件类型筛选
 * - relatedType: 关联类型筛选
 * - relatedId: 关联ID筛选
 * - page: 页码
 * - limit: 每页数量
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再查看文件',
          },
        },
        { status: 401 }
      )
    }

    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '20'), 100)
    const type = url.searchParams.get('type')
    const relatedType = url.searchParams.get('relatedType')
    const relatedId = url.searchParams.get('relatedId')

    // 构建查询条件
    const where: any = {
      uploadedById: session.user.id,
      isDeleted: false,
    }

    if (type) where.type = type
    if (relatedType) where.relatedType = relatedType
    if (relatedId) where.relatedId = relatedId

    // 执行查询
    const [files, total] = await Promise.all([
      prisma.file.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          originalName: true,
          fileName: true,
          filePath: true,
          fileSize: true,
          mimeType: true,
          type: true,
          description: true,
          relatedId: true,
          relatedType: true,
          createdAt: true,
        },
      }),
      prisma.file.count({ where }),
    ])

    return NextResponse.json({
      success: true,
      message: '获取文件列表成功',
      data: files,
      meta: {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取文件列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取文件列表失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 文件验证函数
 */
async function validateFile(file: File, config: any) {
  // 检查文件大小
  if (file.size > config.maxSize) {
    return {
      valid: false,
      error: `文件大小不能超过 ${Math.round(config.maxSize / 1024 / 1024)}MB`,
    }
  }

  // 检查文件类型
  if (!config.allowedTypes.includes(file.type)) {
    return {
      valid: false,
      error: `不支持的文件类型，支持的类型: ${config.allowedTypes.join(', ')}`,
    }
  }

  // 检查文件名
  if (file.name.length > 255) {
    return {
      valid: false,
      error: '文件名太长，不能超过255个字符',
    }
  }

  // 检查文件内容（简单的文件头检查）
  const buffer = await file.arrayBuffer()
  const bytes = new Uint8Array(buffer)

  if (bytes.length === 0) {
    return {
      valid: false,
      error: '文件内容为空',
    }
  }

  // 检查图片文件头
  if (file.type.startsWith('image/')) {
    const isValidImage = validateImageHeader(bytes)
    if (!isValidImage) {
      return {
        valid: false,
        error: '无效的图片文件',
      }
    }
  }

  return { valid: true }
}

/**
 * 验证图片文件头
 */
function validateImageHeader(bytes: Uint8Array): boolean {
  // JPEG: FF D8 FF
  if (bytes[0] === 0xff && bytes[1] === 0xd8 && bytes[2] === 0xff) {
    return true
  }

  // PNG: 89 50 4E 47 0D 0A 1A 0A
  if (
    bytes[0] === 0x89 &&
    bytes[1] === 0x50 &&
    bytes[2] === 0x4e &&
    bytes[3] === 0x47
  ) {
    return true
  }

  // WebP: RIFF ... WEBP
  if (
    bytes[0] === 0x52 &&
    bytes[1] === 0x49 &&
    bytes[2] === 0x46 &&
    bytes[3] === 0x46 &&
    bytes[8] === 0x57 &&
    bytes[9] === 0x45 &&
    bytes[10] === 0x42 &&
    bytes[11] === 0x50
  ) {
    return true
  }

  // GIF: GIF87a or GIF89a
  if (bytes[0] === 0x47 && bytes[1] === 0x49 && bytes[2] === 0x46) {
    return true
  }

  return false
}

/**
 * 更新用户积分
 */
async function updateUserPoints(
  userId: string,
  action: string,
  points: number
) {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        points: { increment: points },
        reputation: { increment: points * 0.1 },
      },
    })
  } catch (error) {
    console.error('更新用户积分失败:', error)
    // 不抛出错误，避免影响主要流程
  }
}
