import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { writeFile, mkdir } from 'fs/promises'
import { existsSync } from 'fs'
import path from 'path'
import crypto from 'crypto'

// 工作文件配置
const WORK_FILE_CONFIG = {
  allowedTypes: [
    'application/pdf',
    'application/msword',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    'image/jpeg',
    'image/png',
    'image/webp',
    'text/plain',
  ],
  maxSize: 10 * 1024 * 1024, // 10MB
  directory: 'work-files',
  maxFilesPerUser: 20, // 每个用户最多上传20个工作文件
  maxFilesPerWorkExperience: 5, // 每个工作经历最多5个文件
}

// MIME类型对应的文件类型描述
const FILE_TYPE_DESCRIPTIONS = {
  'application/pdf': 'PDF文档',
  'application/msword': 'Word文档',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document':
    'Word文档',
  'image/jpeg': '图片',
  'image/png': '图片',
  'image/webp': '图片',
  'text/plain': '文本文件',
}

/**
 * 工作证明文件上传API
 * POST /api/upload/work-files
 *
 * Body (FormData):
 * - file: File - 工作证明文件
 * - workExperienceId?: string - 关联的工作经历ID
 * - description?: string - 文件描述
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再上传工作证明文件',
          },
        },
        { status: 401 }
      )
    }

    // 解析 FormData
    const formData = await request.formData()
    const file = formData.get('file') as File
    const workExperienceId =
      (formData.get('workExperienceId') as string) || null
    const description = (formData.get('description') as string) || ''

    // 验证文件是否存在
    if (!file) {
      return NextResponse.json(
        {
          success: false,
          message: '未选择文件',
          error: {
            code: 'NO_FILE_PROVIDED',
            message: '请选择要上传的工作证明文件',
          },
        },
        { status: 400 }
      )
    }

    // 验证文件类型
    if (!WORK_FILE_CONFIG.allowedTypes.includes(file.type)) {
      return NextResponse.json(
        {
          success: false,
          message: '文件类型不支持',
          error: {
            code: 'INVALID_FILE_TYPE',
            message: `支持的格式: PDF, Word文档, 图片, 文本文件`,
          },
        },
        { status: 400 }
      )
    }

    // 验证文件大小
    if (file.size > WORK_FILE_CONFIG.maxSize) {
      return NextResponse.json(
        {
          success: false,
          message: '文件过大',
          error: {
            code: 'FILE_TOO_LARGE',
            message: `文件大小不能超过 ${Math.round(WORK_FILE_CONFIG.maxSize / 1024 / 1024)}MB`,
          },
        },
        { status: 400 }
      )
    }

    // 验证文件名长度
    if (file.name.length > 255) {
      return NextResponse.json(
        {
          success: false,
          message: '文件名过长',
          error: {
            code: 'FILENAME_TOO_LONG',
            message: '文件名不能超过255个字符',
          },
        },
        { status: 400 }
      )
    }

    // 验证工作经历是否存在且属于当前用户
    if (workExperienceId) {
      const workExperience = await prisma.workExperience.findFirst({
        where: {
          id: workExperienceId,
          userId: session.user.id,
        },
      })

      if (!workExperience) {
        return NextResponse.json(
          {
            success: false,
            message: '工作经历不存在',
            error: {
              code: 'WORK_EXPERIENCE_NOT_FOUND',
              message: '指定的工作经历不存在或无权访问',
            },
          },
          { status: 404 }
        )
      }

      // 检查该工作经历的文件数量限制
      const existingFilesCount = await prisma.experienceFile.count({
        where: { workExperienceId },
      })

      if (existingFilesCount >= WORK_FILE_CONFIG.maxFilesPerWorkExperience) {
        return NextResponse.json(
          {
            success: false,
            message: '文件数量超限',
            error: {
              code: 'TOO_MANY_FILES_PER_EXPERIENCE',
              message: `每个工作经历最多只能上传 ${WORK_FILE_CONFIG.maxFilesPerWorkExperience} 个文件`,
            },
          },
          { status: 400 }
        )
      }
    }

    // 检查用户总文件数量限制
    const userFilesCount = await prisma.experienceFile.count({
      where: {
        workExperience: {
          userId: session.user.id,
        },
      },
    })

    if (userFilesCount >= WORK_FILE_CONFIG.maxFilesPerUser) {
      return NextResponse.json(
        {
          success: false,
          message: '文件数量超限',
          error: {
            code: 'TOO_MANY_FILES_PER_USER',
            message: `每个用户最多只能上传 ${WORK_FILE_CONFIG.maxFilesPerUser} 个工作文件`,
          },
        },
        { status: 400 }
      )
    }

    // 验证文件内容
    const bytes = await file.arrayBuffer()
    const buffer = Buffer.from(bytes)

    if (buffer.length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '文件内容为空',
          error: {
            code: 'EMPTY_FILE',
            message: '请选择有效的文件',
          },
        },
        { status: 400 }
      )
    }

    // 验证文件头（基础检查）
    const isValidFile = validateFileHeader(new Uint8Array(buffer), file.type)
    if (!isValidFile) {
      return NextResponse.json(
        {
          success: false,
          message: '文件格式无效',
          error: {
            code: 'INVALID_FILE_FORMAT',
            message: '文件格式与文件类型不匹配',
          },
        },
        { status: 400 }
      )
    }

    // 生成文件名和路径
    const fileExtension = path.extname(file.name)
    const timestamp = Date.now()
    const fileName = `${session.user.id}-${timestamp}${fileExtension}`
    const uploadDir = path.join(
      process.cwd(),
      'public',
      'uploads',
      WORK_FILE_CONFIG.directory
    )
    const filePath = path.join(uploadDir, fileName)
    const publicUrl = `/uploads/${WORK_FILE_CONFIG.directory}/${fileName}`

    // 确保上传目录存在
    if (!existsSync(uploadDir)) {
      await mkdir(uploadDir, { recursive: true })
    }

    // 保存文件到磁盘
    await writeFile(filePath, buffer)

    // 计算文件哈希值（用于去重检测）
    const fileHash = crypto.createHash('sha256').update(buffer).digest('hex')

    // 如果有关联工作经历，保存到experience_files表
    let savedFile
    if (workExperienceId) {
      savedFile = await prisma.experienceFile.create({
        data: {
          workExperienceId,
          fileName: file.name,
          fileUrl: publicUrl,
          fileSize: file.size,
          mimeType: file.type,
          description:
            description ||
            FILE_TYPE_DESCRIPTIONS[
              file.type as keyof typeof FILE_TYPE_DESCRIPTIONS
            ] ||
            '工作证明文件',
          category: 'DOCUMENT', // 假设ExperienceFile有category字段
        },
        select: {
          id: true,
          fileName: true,
          fileUrl: true,
          fileSize: true,
          mimeType: true,
          description: true,
          category: true,
          verificationStatus: true,
          uploadedAt: true,
          workExperience: {
            select: {
              companyName: true,
              position: true,
            },
          },
        },
      })
    }

    // 更新用户积分（上传工作文件奖励）
    await updateUserPoints(session.user.id, 'WORK_FILE_UPLOADED', 15)

    return NextResponse.json(
      {
        success: true,
        message: '工作证明文件上传成功',
        data: {
          file: savedFile || {
            fileName: file.name,
            fileUrl: publicUrl,
            fileSize: file.size,
            mimeType: file.type,
            description,
            uploadedAt: new Date().toISOString(),
          },
          metadata: {
            originalName: file.name,
            size: file.size,
            type: file.type,
            hash: fileHash,
            uploaded: new Date().toISOString(),
          },
        },
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    console.error('工作文件上传失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '工作文件上传失败',
        error: {
          code: 'UPLOAD_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 获取用户的工作证明文件列表
 * GET /api/upload/work-files
 *
 * 查询参数:
 * - workExperienceId: 筛选特定工作经历的文件
 * - page: 页码
 * - limit: 每页数量
 */
export async function GET(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再查看工作文件',
          },
        },
        { status: 401 }
      )
    }

    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '20'), 50)
    const workExperienceId = url.searchParams.get('workExperienceId')

    // 构建查询条件
    const where: any = {
      workExperience: {
        userId: session.user.id,
      },
    }

    if (workExperienceId) {
      where.workExperienceId = workExperienceId
    }

    // 执行查询
    const [files, total] = await Promise.all([
      prisma.experienceFile.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy: { uploadedAt: 'desc' },
        select: {
          id: true,
          fileName: true,
          fileUrl: true,
          fileSize: true,
          mimeType: true,
          description: true,
          category: true,
          verificationStatus: true,
          uploadedAt: true,
          workExperience: {
            select: {
              id: true,
              companyName: true,
              position: true,
            },
          },
        },
      }),
      prisma.experienceFile.count({ where }),
    ])

    // 统计信息
    const stats = {
      total,
      verified: files.filter(f => f.verificationStatus === 'APPROVED').length,
      pending: files.filter(f => f.verificationStatus === 'PENDING').length,
      totalSize: files.reduce((sum, f) => sum + (f.fileSize || 0), 0),
    }

    return NextResponse.json({
      success: true,
      message: '获取工作文件列表成功',
      data: files,
      meta: {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        stats,
        config: {
          allowedTypes: WORK_FILE_CONFIG.allowedTypes,
          maxSize: WORK_FILE_CONFIG.maxSize,
          maxFilesPerUser: WORK_FILE_CONFIG.maxFilesPerUser,
          maxFilesPerWorkExperience: WORK_FILE_CONFIG.maxFilesPerWorkExperience,
        },
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取工作文件列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取工作文件列表失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 验证文件头
 */
function validateFileHeader(bytes: Uint8Array, mimeType: string): boolean {
  // PDF: %PDF
  if (mimeType === 'application/pdf') {
    const pdfHeader = Array.from(bytes.slice(0, 4))
    return (
      pdfHeader[0] === 0x25 &&
      pdfHeader[1] === 0x50 &&
      pdfHeader[2] === 0x44 &&
      pdfHeader[3] === 0x46
    )
  }

  // JPEG: FF D8 FF
  if (mimeType === 'image/jpeg') {
    return bytes[0] === 0xff && bytes[1] === 0xd8 && bytes[2] === 0xff
  }

  // PNG: 89 50 4E 47 0D 0A 1A 0A
  if (mimeType === 'image/png') {
    return (
      bytes[0] === 0x89 &&
      bytes[1] === 0x50 &&
      bytes[2] === 0x4e &&
      bytes[3] === 0x47
    )
  }

  // WebP: RIFF ... WEBP
  if (mimeType === 'image/webp') {
    return (
      bytes[0] === 0x52 &&
      bytes[1] === 0x49 &&
      bytes[2] === 0x46 &&
      bytes[3] === 0x46 &&
      bytes[8] === 0x57 &&
      bytes[9] === 0x45 &&
      bytes[10] === 0x42 &&
      bytes[11] === 0x50
    )
  }

  // Word文档和文本文件暂时跳过严格验证
  if (mimeType.includes('word') || mimeType === 'text/plain') {
    return bytes.length > 0
  }

  return true // 其他文件类型暂时认为有效
}

/**
 * 更新用户积分
 */
async function updateUserPoints(
  userId: string,
  action: string,
  points: number
) {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        points: { increment: points },
        reputation: { increment: points * 0.1 },
      },
    })
  } catch (error) {
    console.error('更新用户积分失败:', error)
    // 不抛出错误，避免影响主要流程
  }
}
