import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 用户设置更新的验证模式
const updateSettingsSchema = z.object({
  // 隐私设置
  isEmailPublic: z.boolean().optional(),
  isPhonePublic: z.boolean().optional(),
  isAnonymous: z.boolean().optional(),

  // 账户状态
  isActive: z.boolean().optional(),

  // 基本信息（从个人资料API迁移的部分字段）
  username: z
    .string()
    .min(3, '用户名至少3个字符')
    .max(50, '用户名最多50个字符')
    .optional(),
  phone: z
    .string()
    .regex(/^1[3-9]\d{9}$/, '手机号格式不正确')
    .optional(),
})

/**
 * 获取当前用户的设置信息
 * GET /api/users/settings
 *
 * 需要用户登录
 */
export async function GET() {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再获取设置信息',
          },
        },
        { status: 401 }
      )
    }

    // 获取用户设置信息
    const userSettings = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: {
        id: true,
        username: true,
        email: true,
        phone: true,

        // 隐私设置
        isEmailPublic: true,
        isPhonePublic: true,
        isAnonymous: true,

        // 账户状态
        isActive: true,
        isVerified: true,
        isBanned: true,

        // 时间信息
        createdAt: true,
        lastLogin: true,
      },
    })

    if (!userSettings) {
      return NextResponse.json(
        {
          success: false,
          message: '用户不存在',
          error: {
            code: 'USER_NOT_FOUND',
            message: '指定的用户不存在',
          },
        },
        { status: 404 }
      )
    }

    return NextResponse.json({
      success: true,
      message: '获取设置信息成功',
      data: userSettings,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取设置信息失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取设置信息失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 更新当前用户的设置信息
 * PATCH /api/users/settings
 *
 * 需要用户登录
 */
export async function PATCH(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再更新设置信息',
          },
        },
        { status: 401 }
      )
    }

    const body = await request.json()

    // 验证输入数据
    const validatedData = updateSettingsSchema.parse(body)

    // 过滤掉undefined的字段
    const updateData = Object.fromEntries(
      Object.entries(validatedData).filter(([, value]) => value !== undefined)
    )

    // 如果没有要更新的数据
    if (Object.keys(updateData).length === 0) {
      return NextResponse.json(
        {
          success: false,
          message: '没有提供要更新的数据',
          error: {
            code: 'NO_UPDATE_DATA',
            message: '请提供至少一个要更新的字段',
          },
        },
        { status: 400 }
      )
    }

    // 特殊验证：检查用户名是否已被其他用户使用
    if (updateData.username && typeof updateData.username === 'string') {
      const existingUser = await prisma.user.findFirst({
        where: {
          username: updateData.username,
          id: { not: session.user.id }, // 排除当前用户
        },
      })

      if (existingUser) {
        return NextResponse.json(
          {
            success: false,
            message: '用户名已被使用',
            error: {
              code: 'USERNAME_TAKEN',
              message: '该用户名已被其他用户使用，请选择其他用户名',
            },
          },
          { status: 409 }
        )
      }
    }

    // 特殊验证：检查手机号是否已被其他用户使用
    if (updateData.phone && typeof updateData.phone === 'string') {
      const existingUser = await prisma.user.findFirst({
        where: {
          phone: updateData.phone,
          id: { not: session.user.id }, // 排除当前用户
        },
      })

      if (existingUser) {
        return NextResponse.json(
          {
            success: false,
            message: '手机号已被使用',
            error: {
              code: 'PHONE_TAKEN',
              message: '该手机号已被其他用户使用',
            },
          },
          { status: 409 }
        )
      }
    }

    // 更新用户设置
    const updatedSettings = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        ...updateData,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        username: true,
        email: true,
        phone: true,
        isEmailPublic: true,
        isPhonePublic: true,
        isAnonymous: true,
        isActive: true,
        updatedAt: true,
      },
    })

    // 记录重要设置变更（如隐私设置变更）
    const sensitiveFields = [
      'isEmailPublic',
      'isPhonePublic',
      'isAnonymous',
      'isActive',
    ]
    const changedSensitiveFields = Object.keys(updateData).filter(field =>
      sensitiveFields.includes(field)
    )

    if (changedSensitiveFields.length > 0) {
      console.log(
        `用户 ${session.user.id} 更新了敏感设置:`,
        changedSensitiveFields
      )
      // 这里可以添加审计日志记录
    }

    return NextResponse.json({
      success: true,
      message: '设置更新成功',
      data: updatedSettings,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
        changedFields: Object.keys(updateData),
      },
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入的数据格式',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('更新设置失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '更新设置失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 重置用户设置到默认值
 * POST /api/users/settings/reset
 *
 * 需要用户登录
 */
export async function POST(request: NextRequest) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再重置设置',
          },
        },
        { status: 401 }
      )
    }

    const body = await request.json()
    const { confirmReset } = body

    // 确认重置操作
    if (!confirmReset) {
      return NextResponse.json(
        {
          success: false,
          message: '需要确认重置操作',
          error: {
            code: 'RESET_NOT_CONFIRMED',
            message: '请确认要重置设置到默认值',
          },
        },
        { status: 400 }
      )
    }

    // 重置到默认设置
    const resetSettings = await prisma.user.update({
      where: { id: session.user.id },
      data: {
        isEmailPublic: false,
        isPhonePublic: false,
        isAnonymous: false,
        updatedAt: new Date(),
      },
      select: {
        id: true,
        isEmailPublic: true,
        isPhonePublic: true,
        isAnonymous: true,
        updatedAt: true,
      },
    })

    console.log(`用户 ${session.user.id} 重置了设置`)

    return NextResponse.json({
      success: true,
      message: '设置已重置为默认值',
      data: resetSettings,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('重置设置失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '重置设置失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}
