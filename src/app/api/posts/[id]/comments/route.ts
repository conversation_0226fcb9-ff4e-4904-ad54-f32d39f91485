import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 创建评论的验证模式
const createCommentSchema = z.object({
  content: z
    .string()
    .min(1, '评论内容不能为空')
    .max(2000, '评论内容最多2000个字符'),
  parentId: z.string().uuid().optional(), // 父评论ID，用于回复
  isAnonymous: z.boolean().default(false),
})

/**
 * 获取帖子的评论列表
 * GET /api/posts/[id]/comments
 *
 * 查询参数:
 * - page: 页码 (默认 1)
 * - limit: 每页数量 (默认 20)
 * - sort: 排序方式 (latest, popular, oldest)
 * - level: 评论层级 (all, top) - all显示所有评论，top只显示顶级评论
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: postId } = await params
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    const sort = url.searchParams.get('sort') || 'latest'
    const level = url.searchParams.get('level') || 'all'

    // 验证帖子是否存在
    const post = await prisma.post.findFirst({
      where: {
        id: postId,
        isDeleted: false,
        isPublished: true,
      },
      select: { id: true, title: true, commentCount: true },
    })

    if (!post) {
      return NextResponse.json(
        {
          success: false,
          message: '帖子不存在',
          error: {
            code: 'POST_NOT_FOUND',
            message: '指定的帖子不存在或已被删除',
          },
        },
        { status: 404 }
      )
    }

    // 构建查询条件
    const where: any = {
      postId,
      isDeleted: false,
    }

    // 如果只显示顶级评论
    if (level === 'top') {
      where.parentId = null
    }

    // 构建排序条件
    let orderBy: any = {}
    switch (sort) {
      case 'popular':
        orderBy = [{ likeCount: 'desc' }, { createdAt: 'desc' }]
        break
      case 'oldest':
        orderBy = { createdAt: 'asc' }
        break
      case 'latest':
      default:
        orderBy = { createdAt: 'desc' }
        break
    }

    // 执行查询
    const [comments, total] = await Promise.all([
      prisma.comment.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        select: {
          id: true,
          content: true,
          postId: true,
          authorId: true,
          parentId: true,
          isAnonymous: true,
          likeCount: true,
          replyCount: true,
          createdAt: true,
          updatedAt: true,
        },
        orderBy,
      }),
      prisma.comment.count({ where }),
    ])

    // 获取作者信息（非匿名评论）
    const commentsWithAuthor = await Promise.all(
      comments.map(async comment => {
        if (comment.isAnonymous || !comment.authorId) {
          return {
            ...comment,
            author: null,
          }
        }

        const author = await prisma.user.findUnique({
          where: { id: comment.authorId },
          select: {
            id: true,
            name: true,
            avatar: true,
            position: true,
            level: true,
            reputation: true,
            isVerified: true,
          },
        })

        return {
          ...comment,
          author,
        }
      })
    )

    // 如果显示所有评论，需要构建树形结构
    let structuredComments = commentsWithAuthor
    if (level === 'all') {
      structuredComments = await buildCommentTree(commentsWithAuthor)
    }

    return NextResponse.json({
      success: true,
      message: '获取评论列表成功',
      data: {
        post: {
          id: post.id,
          title: post.title,
          totalComments: post.commentCount,
        },
        comments: structuredComments,
      },
      meta: {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        sorting: sort,
        level,
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取评论列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取评论列表失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 为帖子添加评论
 * POST /api/posts/[id]/comments
 *
 * 需要用户登录
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再发表评论',
          },
        },
        { status: 401 }
      )
    }

    const { id: postId } = await params
    const body = await request.json()

    // 验证输入数据
    const validatedData = createCommentSchema.parse(body)

    // 验证帖子是否存在且未被锁定
    const post = await prisma.post.findFirst({
      where: {
        id: postId,
        isDeleted: false,
        isPublished: true,
      },
      select: {
        id: true,
        isLocked: true,
        authorId: true,
      },
    })

    if (!post) {
      return NextResponse.json(
        {
          success: false,
          message: '帖子不存在',
          error: {
            code: 'POST_NOT_FOUND',
            message: '指定的帖子不存在或已被删除',
          },
        },
        { status: 404 }
      )
    }

    if (post.isLocked) {
      return NextResponse.json(
        {
          success: false,
          message: '帖子已被锁定',
          error: {
            code: 'POST_LOCKED',
            message: '该帖子已被管理员锁定，无法评论',
          },
        },
        { status: 403 }
      )
    }

    // 如果是回复评论，验证父评论是否存在
    if (validatedData.parentId) {
      const parentComment = await prisma.comment.findFirst({
        where: {
          id: validatedData.parentId,
          postId: postId,
          isDeleted: false,
        },
        select: { id: true, authorId: true },
      })

      if (!parentComment) {
        return NextResponse.json(
          {
            success: false,
            message: '父评论不存在',
            error: {
              code: 'PARENT_COMMENT_NOT_FOUND',
              message: '要回复的评论不存在或已被删除',
            },
          },
          { status: 404 }
        )
      }
    }

    // 使用事务创建评论并更新相关计数
    const result = await prisma.$transaction(async tx => {
      // 创建评论
      const comment = await tx.comment.create({
        data: {
          content: validatedData.content,
          postId: postId,
          authorId: session.user!.id!,
          parentId: validatedData.parentId,
          isAnonymous: validatedData.isAnonymous,
        },
        select: {
          id: true,
          content: true,
          parentId: true,
          isAnonymous: true,
          createdAt: true,
        },
      })

      // 更新帖子评论数
      await tx.post.update({
        where: { id: postId },
        data: { commentCount: { increment: 1 } },
      })

      // 如果是回复评论，更新父评论的回复数
      if (validatedData.parentId) {
        await tx.comment.update({
          where: { id: validatedData.parentId },
          data: { replyCount: { increment: 1 } },
        })
      }

      return comment
    })

    // 更新用户积分（评论奖励）
    await updateUserPoints(session.user!.id!, 'COMMENT_CREATED', 5)

    return NextResponse.json(
      {
        success: true,
        message: '评论发表成功',
        data: result,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入的数据格式',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('创建评论失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '创建评论失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 构建评论树形结构
 * 将平铺的评论列表转换为嵌套的树形结构
 */
async function buildCommentTree(comments: any[]): Promise<any[]> {
  const commentMap = new Map()
  const topLevelComments: any[] = []

  // 先将所有评论放入 Map 中
  comments.forEach(comment => {
    commentMap.set(comment.id, { ...comment, replies: [] })
  })

  // 构建树形结构
  comments.forEach(comment => {
    const commentWithReplies = commentMap.get(comment.id)

    if (comment.parentId) {
      // 这是一个回复，将其添加到父评论的 replies 数组中
      const parent = commentMap.get(comment.parentId)
      if (parent) {
        parent.replies.push(commentWithReplies)
      }
    } else {
      // 这是顶级评论
      topLevelComments.push(commentWithReplies)
    }
  })

  return topLevelComments
}

/**
 * 更新用户积分
 * 给用户增加积分和声誉值
 */
async function updateUserPoints(
  userId: string,
  action: string,
  points: number
) {
  try {
    await prisma.user.update({
      where: { id: userId },
      data: {
        points: { increment: points },
        reputation: { increment: points * 0.1 },
      },
    })
  } catch (error) {
    console.error('更新用户积分失败:', error)
    // 不抛出错误，避免影响主要流程
  }
}
