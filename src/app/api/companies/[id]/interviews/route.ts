import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 面试难度和结果枚举
const InterviewDifficulty = z.enum(['EASY', 'MEDIUM', 'HARD', 'VERY_HARD'])
const InterviewResult = z.enum(['PASSED', 'FAILED', 'PENDING', 'CANCELLED'])

// 创建面试经验的验证模式
const createInterviewSchema = z.object({
  position: z.string().min(1, '职位名称不能为空').max(100),
  department: z.string().max(100).optional(),
  interviewType: z.string().max(50).optional(),
  interviewRound: z.number().int().min(1).default(1),
  interviewDate: z.string().datetime().optional(),
  duration: z.number().int().min(0).optional(),
  difficulty: InterviewDifficulty,
  result: InterviewResult,
  rating: z.number().int().min(1).max(5).optional(),
  questions: z.array(z.string()).default([]),
  experience: z.string().optional(),
  tips: z.string().optional(),
  notes: z.string().optional(),
  isAnonymous: z.boolean().default(true),
})

/**
 * 获取公司面试经验列表
 * GET /api/companies/[id]/interviews
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    const position = url.searchParams.get('position')
    const difficulty = url.searchParams.get('difficulty')
    const result = url.searchParams.get('result')
    const sort = url.searchParams.get('sort') || 'createdAt'
    const order = url.searchParams.get('order') || 'desc'

    // 验证公司是否存在
    const company = await prisma.company.findFirst({
      where: { id: companyId },
    })

    if (!company) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'COMPANY_NOT_FOUND',
            message: '指定的公司不存在',
          },
        },
        { status: 404 }
      )
    }

    // 构建查询条件
    const where: any = {
      companyId,
    }

    if (position) {
      where.position = { contains: position, mode: 'insensitive' }
    }

    if (difficulty && difficulty !== 'all') {
      where.difficulty = difficulty
    }

    if (result && result !== 'all') {
      where.result = result
    }

    // 构建排序条件
    const orderBy: any = {}
    if (sort === 'difficulty') {
      orderBy.difficulty = order
    } else if (sort === 'result') {
      orderBy.result = order
    } else if (sort === 'rating') {
      orderBy.rating = order
    } else {
      orderBy.createdAt = order
    }

    // 执行查询
    const [interviews, total, stats] = await Promise.all([
      prisma.interview.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        select: {
          id: true,
          position: true,
          department: true,
          interviewType: true,
          interviewRound: true,
          interviewDate: true,
          duration: true,
          difficulty: true,
          result: true,
          rating: true,
          questions: true,
          experience: true,
          tips: true,
          notes: true,
          isAnonymous: true,
          createdAt: true,
        },
        orderBy,
      }),
      prisma.interview.count({ where }),
      // 面试统计
      prisma.interview.aggregate({
        where,
        _avg: {
          rating: true,
          duration: true,
          interviewRound: true,
        },
      }),
    ])

    // 计算通过率
    const passCount = await prisma.interview.count({
      where: { ...where, result: 'PASSED' },
    })
    const passRate = total > 0 ? (passCount / total) * 100 : 0

    // 难度分布
    const difficultyStats = await prisma.interview.groupBy({
      by: ['difficulty'],
      where,
      _count: true,
    })

    return NextResponse.json({
      success: true,
      message: '获取面试经验列表成功',
      data: interviews,
      meta: {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        statistics: {
          averageRating: stats._avg.rating ? Number(stats._avg.rating) : 0,
          averageDuration: stats._avg.duration
            ? Number(stats._avg.duration)
            : 0,
          averageRounds: stats._avg.interviewRound
            ? Number(stats._avg.interviewRound)
            : 0,
          passRate: Math.round(passRate),
          totalPassed: passCount,
          difficultyDistribution: difficultyStats.map(stat => ({
            difficulty: stat.difficulty,
            count: stat._count,
          })),
        },
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取面试经验列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取面试经验列表失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 提交面试经验
 * POST /api/companies/[id]/interviews
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再提交面试经验',
          },
        },
        { status: 401 }
      )
    }

    const { id: companyId } = await params
    const body = await request.json()

    // 验证输入数据
    const validatedData = createInterviewSchema.parse(body)

    // 验证公司是否存在
    const company = await prisma.company.findFirst({
      where: { id: companyId },
    })

    if (!company) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'COMPANY_NOT_FOUND',
            message: '指定的公司不存在',
          },
        },
        { status: 404 }
      )
    }

    // 创建面试经验记录
    const interview = await prisma.interview.create({
      data: {
        ...validatedData,
        authorId: session.user.id,
        companyId,
        interviewDate: validatedData.interviewDate
          ? new Date(validatedData.interviewDate)
          : null,
      },
      select: {
        id: true,
        position: true,
        difficulty: true,
        result: true,
        isAnonymous: true,
        createdAt: true,
      },
    })

    // 更新公司的面试统计
    await updateCompanyInterviewStats(companyId)

    return NextResponse.json(
      {
        success: true,
        message: '提交面试经验成功',
        data: interview,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入的数据格式',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('提交面试经验失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '提交面试经验失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 更新公司面试统计
 */
async function updateCompanyInterviewStats(companyId: string) {
  try {
    // 暂时不更新公司统计字段，因为模型中没有totalInterviews字段
    // 可以在未来添加该字段后启用此功能
    console.log(`面试统计更新请求 - 公司ID: ${companyId}`)
  } catch (error) {
    console.error('更新公司面试统计失败:', error)
    // 不抛出错误，避免影响主要流程
  }
}
