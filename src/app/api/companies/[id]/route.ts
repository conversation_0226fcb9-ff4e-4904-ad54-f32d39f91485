import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 更新公司的验证模式
const updateCompanySchema = z.object({
  name: z.string().min(1).max(200).optional(),
  nameEn: z.string().max(200).optional(),
  logo: z.string().url().max(500).optional(),
  description: z.string().optional(),
  website: z.string().url().max(500).optional(),
  industry: z.string().max(100).optional(),
  size: z
    .enum(['STARTUP', 'SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE'])
    .optional(),
  foundedYear: z
    .number()
    .int()
    .min(1800)
    .max(new Date().getFullYear())
    .optional(),
  headquarters: z.string().max(100).optional(),
  address: z.string().optional(),
  phone: z.string().max(50).optional(),
  email: z.string().email().max(255).optional(),
})

/**
 * 获取单个公司详情
 * GET /api/companies/[id]
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id } = await params

    // 获取公司基本信息
    const company = await prisma.company.findUnique({
      where: {
        id: id,
        isActive: true, // 只显示激活的公司
      },
    })

    if (!company) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'NOT_FOUND',
            message: '指定的公司不存在或已被删除',
          },
        },
        { status: 404 }
      )
    }

    // 并行获取相关数据
    const [
      ratings,
      salaries,
      interviews,
      ratingsCount,
      salariesCount,
      interviewsCount,
    ] = await Promise.all([
      // 获取最新评价
      prisma.rating.findMany({
        where: {
          companyId: id,
          isActive: true,
        },
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          overallRating: true,
          workLifeBalance: true,
          compensation: true,
          culture: true,
          careerGrowth: true,
          management: true,
          title: true,
          pros: true,
          cons: true,
          advice: true,
          isRecommended: true,
          recommendationReason: true,
          position: true,
          department: true,
          workDuration: true,
          employmentType: true,
          isAnonymous: true,
          isVerified: true,
          createdAt: true,
        },
      }),
      // 获取最新薪资数据
      prisma.salary.findMany({
        where: {
          companyId: id,
          isActive: true,
        },
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          position: true,
          level: true,
          department: true,
          workLocation: true,
          workType: true,
          experience: true,
          education: true,
          baseSalary: true,
          bonus: true,
          stockOptions: true,
          benefits: true,
          totalSalary: true,
          currency: true,
          salaryYear: true,
          isAnonymous: true,
          isVerified: true,
          createdAt: true,
        },
      }),
      // 获取最新面试经验
      prisma.interview.findMany({
        where: {
          companyId: id,
          isActive: true,
        },
        take: 5,
        orderBy: { createdAt: 'desc' },
        select: {
          id: true,
          position: true,
          department: true,
          interviewType: true,
          interviewRound: true,
          interviewDate: true,
          duration: true,
          difficulty: true,
          result: true,
          rating: true,
          questions: true,
          experience: true,
          tips: true,
          isAnonymous: true,
          createdAt: true,
        },
      }),
      // 统计数量
      prisma.rating.count({
        where: {
          companyId: id,
          isActive: true,
        },
      }),
      prisma.salary.count({
        where: {
          companyId: id,
          isActive: true,
        },
      }),
      prisma.interview.count({
        where: {
          companyId: id,
          isActive: true,
        },
      }),
    ])

    // 组装完整的公司数据
    const companyWithRelations = {
      ...company,
      ratings,
      salaries,
      interviews,
      _count: {
        ratings: ratingsCount,
        salaries: salariesCount,
        interviews: interviewsCount,
      },
    }

    return NextResponse.json({
      success: true,
      message: '获取公司详情成功',
      data: companyWithRelations,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取公司详情失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取公司详情失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 更新公司信息
 * PATCH /api/companies/[id]
 *
 * 需要管理员权限
 */
export async function PATCH(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录',
          },
        },
        { status: 401 }
      )
    }

    // 检查用户权限 - 只有管理员可以更新公司
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { level: true },
    })

    if (user?.level !== 'ADMIN' && user?.level !== 'MODERATOR') {
      return NextResponse.json(
        {
          success: false,
          message: '权限不足',
          error: {
            code: 'INSUFFICIENT_PERMISSION',
            message: '只有管理员或版主可以更新企业信息',
          },
        },
        { status: 403 }
      )
    }

    const body = await request.json()

    // 验证输入数据
    const validatedData = updateCompanySchema.parse(body)

    // 检查公司是否存在
    const existingCompany = await prisma.company.findUnique({
      where: { id: params.id },
    })

    if (!existingCompany) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'NOT_FOUND',
            message: '指定的公司不存在',
          },
        },
        { status: 404 }
      )
    }

    // 如果更新公司名，检查是否重复
    if (validatedData.name || validatedData.nameEn) {
      const duplicateCompany = await prisma.company.findFirst({
        where: {
          AND: [
            { id: { not: params.id } }, // 排除当前公司
            {
              OR: [
                validatedData.name ? { name: validatedData.name } : {},
                validatedData.nameEn ? { nameEn: validatedData.nameEn } : {},
              ].filter(obj => Object.keys(obj).length > 0),
            },
          ],
        },
      })

      if (duplicateCompany) {
        return NextResponse.json(
          {
            success: false,
            message: '公司名称已存在',
            error: {
              code: 'DUPLICATE_COMPANY',
              message: '该公司名称已被其他公司使用',
            },
          },
          { status: 409 }
        )
      }
    }

    // 更新公司
    const updatedCompany = await prisma.company.update({
      where: { id: params.id },
      data: {
        ...validatedData,
        updatedAt: new Date(),
      },
    })

    return NextResponse.json({
      success: true,
      message: '更新公司成功',
      data: updatedCompany,
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '输入数据格式不正确',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('更新公司失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '更新公司失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 删除公司（软删除）
 * DELETE /api/companies/[id]
 *
 * 需要管理员权限
 */
export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录',
          },
        },
        { status: 401 }
      )
    }

    // 检查用户权限 - 只有管理员可以删除公司
    const user = await prisma.user.findUnique({
      where: { id: session.user.id },
      select: { level: true },
    })

    if (user?.level !== 'ADMIN' && user?.level !== 'MODERATOR') {
      return NextResponse.json(
        {
          success: false,
          message: '权限不足',
          error: {
            code: 'INSUFFICIENT_PERMISSION',
            message: '只有管理员或版主可以删除企业信息',
          },
        },
        { status: 403 }
      )
    }

    // 检查公司是否存在
    const existingCompany = await prisma.company.findUnique({
      where: { id: params.id },
    })

    if (!existingCompany) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'NOT_FOUND',
            message: '指定的公司不存在',
          },
        },
        { status: 404 }
      )
    }

    // 软删除公司（设置 isActive = false）
    const deletedCompany = await prisma.company.update({
      where: { id: params.id },
      data: {
        isActive: false,
        updatedAt: new Date(),
      },
    })

    return NextResponse.json({
      success: true,
      message: '删除公司成功',
      data: {
        id: deletedCompany.id,
        name: deletedCompany.name,
      },
      meta: {
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('删除公司失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '删除公司失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}
