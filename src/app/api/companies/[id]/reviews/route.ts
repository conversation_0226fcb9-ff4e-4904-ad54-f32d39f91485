import { auth } from '@/lib/auth'
import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'
import { z } from 'zod'

// 创建评价的验证模式
const createReviewSchema = z.object({
  overallRating: z.number().min(1).max(5),
  workLifeBalance: z.number().int().min(1).max(5).optional(),
  compensation: z.number().int().min(1).max(5).optional(),
  culture: z.number().int().min(1).max(5).optional(),
  careerGrowth: z.number().int().min(1).max(5).optional(),
  management: z.number().int().min(1).max(5).optional(),
  title: z.string().max(200).optional(),
  pros: z.string().optional(),
  cons: z.string().optional(),
  advice: z.string().optional(),
  isRecommended: z.boolean().optional(),
  recommendationReason: z.string().optional(),
  position: z.string().max(100).optional(),
  department: z.string().max(100).optional(),
  workDuration: z.number().int().min(0).max(50).optional(),
  employmentType: z.string().max(50).optional(),
  isAnonymous: z.boolean().default(true),
})

/**
 * 获取公司评价列表
 * GET /api/companies/[id]/reviews
 */
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    const { id: companyId } = await params
    const url = new URL(request.url)
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = parseInt(url.searchParams.get('limit') || '20')
    const rating = url.searchParams.get('rating')
    const position = url.searchParams.get('position')
    const employmentType = url.searchParams.get('employmentType')
    const sort = url.searchParams.get('sort') || 'createdAt'
    const order = url.searchParams.get('order') || 'desc'

    // 验证公司是否存在且激活
    const company = await prisma.company.findFirst({
      where: { id: companyId, isActive: true },
    })

    if (!company) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'COMPANY_NOT_FOUND',
            message: '指定的公司不存在',
          },
        },
        { status: 404 }
      )
    }

    // 构建查询条件
    const where: any = {
      companyId,
      isActive: true,
    }

    if (rating && rating !== 'all') {
      const ratingValue = parseInt(rating)
      where.overallRating = { gte: ratingValue, lt: ratingValue + 1 }
    }

    if (position) {
      where.position = { contains: position, mode: 'insensitive' }
    }

    if (employmentType && employmentType !== 'all') {
      where.employmentType = employmentType
    }

    // 构建排序条件
    const orderBy: any = {}
    if (sort === 'overallRating') {
      orderBy.overallRating = order
    } else {
      orderBy.createdAt = order
    }

    // 执行查询
    const [reviews, total, stats] = await Promise.all([
      prisma.rating.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        select: {
          id: true,
          overallRating: true,
          workLifeBalance: true,
          compensation: true,
          culture: true,
          careerGrowth: true,
          management: true,
          title: true,
          pros: true,
          cons: true,
          advice: true,
          isRecommended: true,
          recommendationReason: true,
          position: true,
          department: true,
          workDuration: true,
          employmentType: true,
          isAnonymous: true,
          isVerified: true,
          createdAt: true,
        },
        orderBy,
      }),
      prisma.rating.count({ where }),
      // 评价统计
      prisma.rating.aggregate({
        where,
        _avg: {
          overallRating: true,
          compensation: true,
          culture: true,
          management: true,
          careerGrowth: true,
          workLifeBalance: true,
        },
        _count: true,
      }),
    ])

    // 计算推荐率
    const recommendedCount = await prisma.rating.count({
      where: { ...where, isRecommended: true },
    })
    const recommendationRate = total > 0 ? (recommendedCount / total) * 100 : 0

    return NextResponse.json({
      success: true,
      message: '获取评价列表成功',
      data: reviews,
      meta: {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        statistics: {
          averageOverall: stats._avg.overallRating
            ? Number(stats._avg.overallRating)
            : 0,
          averageWorkLife: stats._avg.workLifeBalance || 0,
          averageCompensation: stats._avg.compensation || 0,
          averageCulture: stats._avg.culture || 0,
          averageGrowth: stats._avg.careerGrowth || 0,
          averageManagement: stats._avg.management || 0,
          recommendationRate: Math.round(recommendationRate),
          totalRecommended: recommendedCount,
        },
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('获取评价列表失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '获取评价列表失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 提交公司评价
 * POST /api/companies/[id]/reviews
 */
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ id: string }> }
) {
  try {
    // 验证用户权限
    const session = await auth()
    if (!session?.user?.id) {
      return NextResponse.json(
        {
          success: false,
          message: '需要登录',
          error: {
            code: 'UNAUTHORIZED',
            message: '请先登录后再提交评价',
          },
        },
        { status: 401 }
      )
    }

    const { id: companyId } = await params
    const body = await request.json()

    // 验证输入数据
    const validatedData = createReviewSchema.parse(body)

    // 验证公司是否存在
    const company = await prisma.company.findFirst({
      where: { id: companyId, isActive: true },
    })

    if (!company) {
      return NextResponse.json(
        {
          success: false,
          message: '公司不存在',
          error: {
            code: 'COMPANY_NOT_FOUND',
            message: '指定的公司不存在',
          },
        },
        { status: 404 }
      )
    }

    // 检查用户是否已为该公司提交过评价
    const existingReview = await prisma.rating.findFirst({
      where: {
        authorId: session.user.id,
        companyId,
      },
    })

    if (existingReview) {
      return NextResponse.json(
        {
          success: false,
          message: '您已为该公司提交过评价',
          error: {
            code: 'DUPLICATE_REVIEW',
            message: '每个用户只能为每家公司提交一次评价',
          },
        },
        { status: 409 }
      )
    }

    // 创建评价记录
    const review = await prisma.rating.create({
      data: {
        ...validatedData,
        authorId: session.user.id,
        companyId,
        isVerified: false,
      },
      select: {
        id: true,
        overallRating: true,
        title: true,
        position: true,
        isAnonymous: true,
        createdAt: true,
      },
    })

    // 更新公司的评价统计
    await updateCompanyReviewStats(companyId)

    return NextResponse.json(
      {
        success: true,
        message: '提交评价成功',
        data: review,
        meta: {
          timestamp: new Date().toISOString(),
          version: '1.0',
        },
      },
      { status: 201 }
    )
  } catch (error) {
    if (error instanceof z.ZodError) {
      return NextResponse.json(
        {
          success: false,
          message: '输入数据验证失败',
          error: {
            code: 'VALIDATION_ERROR',
            message: '请检查输入的数据格式',
            details: error.errors.reduce(
              (acc, err) => {
                acc[err.path.join('.')] = err.message
                return acc
              },
              {} as Record<string, string>
            ),
          },
        },
        { status: 400 }
      )
    }

    console.error('提交评价失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '提交评价失败',
        error: {
          code: 'DATABASE_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 更新公司评价统计
 */
async function updateCompanyReviewStats(companyId: string) {
  try {
    const stats = await prisma.rating.aggregate({
      where: { companyId, isActive: true },
      _count: true,
      _avg: {
        overallRating: true,
      },
    })

    await prisma.company.update({
      where: { id: companyId },
      data: {
        totalReviews: stats._count || 0,
        averageRating: stats._avg.overallRating
          ? parseFloat(stats._avg.overallRating.toString())
          : null,
        totalRatings: stats._count || 0,
      },
    })
  } catch (error) {
    console.error('更新公司评价统计失败:', error)
    // 不抛出错误，避免影响主要流程
  }
}
