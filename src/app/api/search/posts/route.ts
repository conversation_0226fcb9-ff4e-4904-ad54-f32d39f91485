import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'

/**
 * 帖子搜索API
 * GET /api/search/posts
 *
 * 查询参数:
 * - q: 搜索关键词（搜索标题、内容、摘要）
 * - type: 帖子类型筛选 (DISCUSSION, QUESTION, SHARING, NEWS, REVIEW, JOB)
 * - category: 分类筛选
 * - tags: 标签筛选（逗号分隔）
 * - companyId: 关联企业筛选
 * - authorId: 作者筛选
 * - anonymous: 是否只显示匿名帖子
 * - hasComments: 是否只显示有评论的帖子
 * - minLikes: 最少点赞数
 * - dateFrom: 开始日期 (YYYY-MM-DD)
 * - dateTo: 结束日期 (YYYY-MM-DD)
 * - sort: 排序方式 (relevance, latest, popular, hot, comments)
 * - page: 页码
 * - limit: 每页数量
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const query = url.searchParams.get('q') || ''
    const type = url.searchParams.get('type')
    const category = url.searchParams.get('category')
    const tagsParam = url.searchParams.get('tags')
    const companyId = url.searchParams.get('companyId')
    const authorId = url.searchParams.get('authorId')
    const anonymous = url.searchParams.get('anonymous') === 'true'
    const hasComments = url.searchParams.get('hasComments') === 'true'
    const minLikes = url.searchParams.get('minLikes')
    const dateFrom = url.searchParams.get('dateFrom')
    const dateTo = url.searchParams.get('dateTo')
    const sort = url.searchParams.get('sort') || 'relevance'
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '20'), 100)

    // 解析标签
    const tags = tagsParam
      ? tagsParam
          .split(',')
          .map(tag => tag.trim())
          .filter(Boolean)
      : []

    // 构建搜索条件
    const where: any = {
      isPublished: true,
      isDeleted: false,
    }

    // 文本搜索
    if (query.trim()) {
      where.OR = [
        { title: { contains: query, mode: 'insensitive' } },
        { content: { contains: query, mode: 'insensitive' } },
        { excerpt: { contains: query, mode: 'insensitive' } },
      ]
    }

    // 帖子类型筛选
    if (
      type &&
      ['DISCUSSION', 'QUESTION', 'SHARING', 'NEWS', 'REVIEW', 'JOB'].includes(
        type
      )
    ) {
      where.type = type
    }

    // 分类筛选
    if (category) {
      where.category = category
    }

    // 标签筛选
    if (tags.length > 0) {
      where.tags = {
        hasEvery: tags, // 包含所有指定标签
      }
    }

    // 企业筛选
    if (companyId) {
      where.companyId = companyId
    }

    // 作者筛选
    if (authorId) {
      where.authorId = authorId
    }

    // 匿名筛选
    if (anonymous) {
      where.isAnonymous = true
    }

    // 有评论筛选
    if (hasComments) {
      where.commentCount = { gt: 0 }
    }

    // 最少点赞数筛选
    if (minLikes) {
      const likes = parseInt(minLikes)
      if (!isNaN(likes) && likes >= 0) {
        where.likeCount = { gte: likes }
      }
    }

    // 日期范围筛选
    if (dateFrom || dateTo) {
      where.createdAt = {}
      if (dateFrom) {
        const fromDate = new Date(dateFrom)
        if (!isNaN(fromDate.getTime())) {
          where.createdAt.gte = fromDate
        }
      }
      if (dateTo) {
        const toDate = new Date(dateTo)
        toDate.setHours(23, 59, 59, 999) // 包含整天
        if (!isNaN(toDate.getTime())) {
          where.createdAt.lte = toDate
        }
      }
    }

    // 排序配置
    let orderBy: any = []
    switch (sort) {
      case 'latest':
        orderBy = [{ createdAt: 'desc' }]
        break
      case 'popular':
        orderBy = [
          { likeCount: 'desc' },
          { viewCount: 'desc' },
          { createdAt: 'desc' },
        ]
        break
      case 'hot':
        // 热度算法：根据点赞、评论、浏览和时间加权
        orderBy = [
          { likeCount: 'desc' },
          { commentCount: 'desc' },
          { viewCount: 'desc' },
          { createdAt: 'desc' },
        ]
        break
      case 'comments':
        orderBy = [
          { commentCount: 'desc' },
          { likeCount: 'desc' },
          { createdAt: 'desc' },
        ]
        break
      case 'relevance':
      default:
        if (query.trim()) {
          // 相关性排序时，优先考虑标题匹配
          orderBy = [
            { isPinned: 'desc' }, // 置顶帖子优先
            { likeCount: 'desc' },
            { commentCount: 'desc' },
            { createdAt: 'desc' },
          ]
        } else {
          // 无搜索词时按热度排序
          orderBy = [
            { isPinned: 'desc' },
            { likeCount: 'desc' },
            { commentCount: 'desc' },
            { createdAt: 'desc' },
          ]
        }
        break
    }

    // 执行搜索
    const [posts, total] = await Promise.all([
      prisma.post.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy,
        select: {
          id: true,
          title: true,
          content: true,
          excerpt: true,
          type: true,
          category: true,
          tags: true,
          companyId: true,
          authorId: true,
          isAnonymous: true,
          isPinned: true,
          viewCount: true,
          likeCount: true,
          commentCount: true,
          shareCount: true,
          createdAt: true,
          updatedAt: true,
          publishedAt: true,
        },
      }),
      prisma.post.count({ where }),
    ])

    // 增强搜索结果（添加相关性评分和高亮）
    const enhancedPosts = posts.map(post => {
      let relevanceScore = 0
      const highlights: string[] = []

      if (query.trim()) {
        const lowerQuery = query.toLowerCase()
        const lowerTitle = post.title.toLowerCase()
        const lowerContent = post.content.toLowerCase()
        const lowerExcerpt = post.excerpt?.toLowerCase() || ''

        // 计算相关性评分
        if (lowerTitle.includes(lowerQuery)) {
          relevanceScore += 100
          highlights.push('title')
        }
        if (lowerExcerpt.includes(lowerQuery)) {
          relevanceScore += 60
          highlights.push('excerpt')
        }
        if (lowerContent.includes(lowerQuery)) {
          relevanceScore += 40
          highlights.push('content')
        }

        // 标签匹配加分
        if (post.tags.some(tag => tag.toLowerCase().includes(lowerQuery))) {
          relevanceScore += 80
          highlights.push('tags')
        }
      }

      return {
        ...post,
        relevanceScore,
        highlights,
        // 格式化显示数据
        typeDisplay: getTypeDisplay(post.type),
        timeAgo: getTimeAgo(post.createdAt),
        engagementScore:
          (post.likeCount || 0) +
          (post.commentCount || 0) * 2 +
          (post.viewCount || 0) * 0.1,
        contentPreview: post.excerpt || truncateContent(post.content, 200),
        isRecent: isRecent(post.createdAt),
        authorDisplay: post.isAnonymous ? '匿名用户' : null, // 后续可以关联用户信息
      }
    })

    // 如果是相关性排序，重新按相关性评分排序
    if (sort === 'relevance' && query.trim()) {
      enhancedPosts.sort((a, b) => b.relevanceScore - a.relevanceScore)
    }

    // 统计信息
    const stats = {
      total,
      anonymous: posts.filter(p => p.isAnonymous).length,
      pinned: posts.filter(p => p.isPinned).length,
      withComments: posts.filter(p => (p.commentCount || 0) > 0).length,
      types: getTypeStats(posts),
      categories: getCategoryStats(posts),
      popularTags: getPopularTags(posts),
    }

    // 搜索建议
    const suggestions = await generatePostSearchSuggestions(
      query,
      type,
      category
    )

    return NextResponse.json({
      success: true,
      message: `找到 ${total} 个帖子`,
      data: enhancedPosts,
      meta: {
        query: {
          text: query,
          type,
          category,
          tags: tags.length > 0 ? tags : undefined,
          companyId,
          authorId,
          anonymous,
          hasComments,
          minLikes,
          dateFrom,
          dateTo,
          sort,
        },
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        stats,
        suggestions,
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('帖子搜索失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '搜索失败',
        error: {
          code: 'SEARCH_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 获取帖子类型显示名称
 */
function getTypeDisplay(type: string | null): string {
  const typeMap: Record<string, string> = {
    DISCUSSION: '讨论',
    QUESTION: '提问',
    SHARING: '分享',
    NEWS: '资讯',
    REVIEW: '评价',
    JOB: '招聘',
  }
  return type ? typeMap[type] || type : '未分类'
}

/**
 * 获取时间距现在的描述
 */
function getTimeAgo(date: Date | null): string {
  if (!date) return '未知时间'

  const now = new Date()
  const diffMs = now.getTime() - new Date(date).getTime()
  const diffMins = Math.floor(diffMs / (1000 * 60))
  const diffHours = Math.floor(diffMs / (1000 * 60 * 60))
  const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

  if (diffMins < 1) return '刚刚'
  if (diffMins < 60) return `${diffMins}分钟前`
  if (diffHours < 24) return `${diffHours}小时前`
  if (diffDays < 30) return `${diffDays}天前`

  return new Date(date).toLocaleDateString('zh-CN')
}

/**
 * 截断内容
 */
function truncateContent(content: string, maxLength: number): string {
  if (content.length <= maxLength) return content
  return content.substring(0, maxLength) + '...'
}

/**
 * 判断是否为最近发布
 */
function isRecent(date: Date | null): boolean {
  if (!date) return false
  const now = new Date()
  const diffHours =
    (now.getTime() - new Date(date).getTime()) / (1000 * 60 * 60)
  return diffHours <= 24 // 24小时内为最近
}

/**
 * 获取类型统计
 */
function getTypeStats(posts: any[]): Record<string, number> {
  const stats: Record<string, number> = {}
  posts.forEach(post => {
    if (post.type) {
      stats[post.type] = (stats[post.type] || 0) + 1
    }
  })
  return stats
}

/**
 * 获取分类统计
 */
function getCategoryStats(posts: any[]): Record<string, number> {
  const stats: Record<string, number> = {}
  posts.forEach(post => {
    if (post.category) {
      stats[post.category] = (stats[post.category] || 0) + 1
    }
  })
  return stats
}

/**
 * 获取热门标签
 */
function getPopularTags(posts: any[]): Array<{ tag: string; count: number }> {
  const tagCounts: Record<string, number> = {}

  posts.forEach(post => {
    if (post.tags && Array.isArray(post.tags)) {
      post.tags.forEach((tag: string) => {
        tagCounts[tag] = (tagCounts[tag] || 0) + 1
      })
    }
  })

  return Object.entries(tagCounts)
    .map(([tag, count]) => ({ tag, count }))
    .sort((a, b) => b.count - a.count)
    .slice(0, 10)
}

/**
 * 生成帖子搜索建议
 */
async function generatePostSearchSuggestions(
  query: string,
  type?: string | null,
  category?: string | null
) {
  try {
    const suggestions: any = {
      posts: [],
      tags: [],
      categories: [],
      relatedTerms: [],
    }

    // 帖子标题建议
    if (query.trim()) {
      const postSuggestions = await prisma.post.findMany({
        where: {
          isPublished: true,
          isDeleted: false,
          title: {
            contains: query,
            mode: 'insensitive',
          },
        },
        select: {
          title: true,
          type: true,
        },
        take: 5,
      })
      suggestions.posts = postSuggestions.map(p => p.title)
    }

    // 热门标签建议
    const tagsData = await prisma.post.findMany({
      where: {
        isPublished: true,
        isDeleted: false,
        tags: { not: { equals: [] } },
      },
      select: {
        tags: true,
      },
      take: 100,
    })

    const allTags: string[] = []
    tagsData.forEach(post => {
      if (post.tags) allTags.push(...post.tags)
    })

    const tagCounts: Record<string, number> = {}
    allTags.forEach(tag => {
      tagCounts[tag] = (tagCounts[tag] || 0) + 1
    })

    suggestions.tags = Object.entries(tagCounts)
      .map(([tag, count]) => ({ name: tag, count }))
      .sort((a, b) => b.count - a.count)
      .slice(0, 10)

    // 分类建议
    const categoryData = await prisma.post.groupBy({
      by: ['category'],
      where: {
        isPublished: true,
        isDeleted: false,
        category: { not: null },
      },
      _count: {
        category: true,
      },
      orderBy: {
        _count: {
          category: 'desc',
        },
      },
      take: 10,
    })

    suggestions.categories = categoryData.map(item => ({
      name: item.category,
      count: item._count.category,
    }))

    return suggestions
  } catch (error) {
    console.error('生成帖子搜索建议失败:', error)
    return { posts: [], tags: [], categories: [], relatedTerms: [] }
  }
}
