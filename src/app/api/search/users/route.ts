import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'

/**
 * 用户搜索 API
 * GET /api/search/users
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const q = url.searchParams.get('q') || ''
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '20'), 50)
    const sort = url.searchParams.get('sort') || 'relevance'

    // 如果没有搜索词，返回空结果
    if (!q.trim()) {
      return NextResponse.json({
        success: true,
        message: '请输入搜索关键词',
        data: [],
        meta: {
          pagination: { page, limit, total: 0, totalPages: 0 },
          searchQuery: q,
          timestamp: new Date().toISOString(),
        },
      })
    }

    // 构建搜索条件
    const where = {
      isActive: true,
      // 只搜索已验证的用户
      OR: [
        { name: { contains: q, mode: 'insensitive' as const } },
        { username: { contains: q, mode: 'insensitive' as const } },
        { bio: { contains: q, mode: 'insensitive' as const } },
      ],
    }

    // 构建排序条件
    let orderBy: any = []
    switch (sort) {
      case 'name':
        orderBy = [{ name: 'asc' }]
        break
      case 'reputation':
        orderBy = [{ reputation: 'desc' }, { points: 'desc' }]
        break
      case 'latest':
        orderBy = [{ createdAt: 'desc' }]
        break
      default: // relevance
        orderBy = [
          { reputation: 'desc' },
          { points: 'desc' },
          { createdAt: 'desc' },
        ]
    }

    // 执行搜索
    const [users, total] = await Promise.all([
      prisma.user.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy,
        select: {
          id: true,
          name: true,
          username: true,
          avatar: true,
          bio: true,
          position: true,
          company: true,
          experience: true,
          industry: true,
          isVerified: true,
          reputation: true,
          points: true,
          createdAt: true,
        },
      }),
      prisma.user.count({ where }),
    ])

    // 格式化结果
    const formattedUsers = users.map(user => ({
      id: user.id,
      name: user.name,
      username: user.username,
      avatar: user.avatar,
      bio: user.bio,
      position: user.position,
      company: user.company,
      experience: user.experience,
      industry: user.industry,
      isVerified: user.isVerified,
      reputation: user.reputation,
      points: user.points,
      // 简化的显示信息
      displayName: user.name || user.username || '匿名用户',
      subtitle: [
        user.position && user.company
          ? `${user.position} @ ${user.company}`
          : null,
        user.industry ? `${user.industry}行业` : null,
        `声誉: ${user.reputation || 0}`,
        `积分: ${user.points || 0}`,
      ]
        .filter(Boolean)
        .join(' • '),
      url: `/profile/${user.username || user.id}`,
      createdAt: user.createdAt,
    }))

    return NextResponse.json({
      success: true,
      message: `找到 ${total} 位用户`,
      data: formattedUsers,
      meta: {
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        searchQuery: q,
        sort,
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('用户搜索失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '搜索失败',
        error: {
          code: 'SEARCH_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}
