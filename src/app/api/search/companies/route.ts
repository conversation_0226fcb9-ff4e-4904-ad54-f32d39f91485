import { prisma } from '@/lib/prisma'
import { NextRequest, NextResponse } from 'next/server'

/**
 * 企业搜索API
 * GET /api/search/companies
 *
 * 查询参数:
 * - q: 搜索关键词（搜索企业名称和描述）
 * - industry: 行业筛选
 * - size: 企业规模筛选
 * - location: 地理位置筛选
 * - verified: 是否只显示已验证企业
 * - minRating: 最低评分筛选
 * - hasReviews: 是否只显示有评价的企业
 * - hasSalaries: 是否只显示有薪资数据的企业
 * - hasJobs: 是否只显示有招聘信息的企业
 * - sort: 排序方式 (relevance, rating, name, size, recent)
 * - page: 页码
 * - limit: 每页数量
 */
export async function GET(request: NextRequest) {
  try {
    const url = new URL(request.url)
    const query = url.searchParams.get('q') || ''
    const industry = url.searchParams.get('industry')
    const size = url.searchParams.get('size')
    const location = url.searchParams.get('location')
    const verified = url.searchParams.get('verified') === 'true'
    const minRating = url.searchParams.get('minRating')
    const hasReviews = url.searchParams.get('hasReviews') === 'true'
    const hasSalaries = url.searchParams.get('hasSalaries') === 'true'

    const sort = url.searchParams.get('sort') || 'relevance'
    const page = parseInt(url.searchParams.get('page') || '1')
    const limit = Math.min(parseInt(url.searchParams.get('limit') || '20'), 100)

    // 构建搜索条件
    const where: any = {
      isActive: true, // 只搜索激活的企业
    }

    // 文本搜索
    if (query.trim()) {
      where.OR = [
        { name: { contains: query, mode: 'insensitive' } },
        { nameEn: { contains: query, mode: 'insensitive' } },
        { description: { contains: query, mode: 'insensitive' } },
      ]
    }

    // 行业筛选
    if (industry) {
      where.industry = industry
    }

    // 企业规模筛选
    if (
      size &&
      ['STARTUP', 'SMALL', 'MEDIUM', 'LARGE', 'ENTERPRISE'].includes(size)
    ) {
      where.size = size
    }

    // 地理位置筛选
    if (location) {
      where.OR = [
        ...(where.OR || []),
        { headquarters: { contains: location, mode: 'insensitive' } },
        { address: { contains: location, mode: 'insensitive' } },
      ]
    }

    // 验证状态筛选
    if (verified) {
      where.isVerified = true
    }

    // 最低评分筛选
    if (minRating) {
      const rating = parseFloat(minRating)
      if (!isNaN(rating) && rating >= 0 && rating <= 5) {
        where.averageRating = { gte: rating }
      }
    }

    // 有评价筛选
    if (hasReviews) {
      where.totalReviews = { gt: 0 }
    }

    // 有薪资数据筛选
    if (hasSalaries) {
      where.totalSalaries = { gt: 0 }
    }

    // 排序配置
    let orderBy: any = []
    switch (sort) {
      case 'rating':
        orderBy = [
          { averageRating: 'desc' },
          { totalRatings: 'desc' },
          { name: 'asc' },
        ]
        break
      case 'name':
        orderBy = [{ name: 'asc' }]
        break
      case 'size':
        orderBy = [
          {
            size: {
              sort: 'asc',
              // 自定义排序：STARTUP < SMALL < MEDIUM < LARGE < ENTERPRISE
            },
          },
          { name: 'asc' },
        ]
        break
      case 'recent':
        orderBy = [{ createdAt: 'desc' }]
        break
      case 'relevance':
      default:
        if (query.trim()) {
          // 相关性排序：精确匹配 > 前缀匹配 > 包含匹配
          orderBy = [
            { averageRating: 'desc' },
            { totalRatings: 'desc' },
            { name: 'asc' },
          ]
        } else {
          // 无搜索词时按评分排序
          orderBy = [
            { averageRating: 'desc' },
            { totalRatings: 'desc' },
            { name: 'asc' },
          ]
        }
        break
    }

    // 执行搜索
    const [companies, total] = await Promise.all([
      prisma.company.findMany({
        where,
        skip: (page - 1) * limit,
        take: limit,
        orderBy,
        select: {
          id: true,
          name: true,
          nameEn: true,
          logo: true,
          description: true,
          website: true,
          industry: true,
          size: true,
          foundedYear: true,
          headquarters: true,
          isVerified: true,
          totalRatings: true,
          averageRating: true,
          totalSalaries: true,
          totalReviews: true,
          createdAt: true,
        },
      }),
      prisma.company.count({ where }),
    ])

    // 增强搜索结果（添加匹配高亮和相关性评分）
    const enhancedCompanies = companies.map(company => {
      let relevanceScore = 0
      const highlights: string[] = []

      if (query.trim()) {
        const lowerQuery = query.toLowerCase()
        const lowerName = company.name.toLowerCase()
        const lowerDesc = company.description?.toLowerCase() || ''

        // 计算相关性评分
        if (lowerName === lowerQuery) relevanceScore += 100
        else if (lowerName.startsWith(lowerQuery)) relevanceScore += 80
        else if (lowerName.includes(lowerQuery)) relevanceScore += 60

        if (lowerDesc.includes(lowerQuery)) relevanceScore += 20

        // 生成高亮标记
        if (lowerName.includes(lowerQuery)) highlights.push('name')
        if (lowerDesc.includes(lowerQuery)) highlights.push('description')
      }

      return {
        ...company,
        relevanceScore,
        highlights,
        // 格式化显示数据
        displayName:
          company.nameEn && company.nameEn !== company.name
            ? `${company.name} (${company.nameEn})`
            : company.name,
        ratingDisplay: company.averageRating
          ? `${Number(company.averageRating).toFixed(1)}分 (${company.totalRatings}条评价)`
          : '暂无评价',
        sizeDisplay: getSizeDisplay(company.size),
        establishedDisplay: company.foundedYear
          ? `${new Date().getFullYear() - company.foundedYear}年`
          : null,
      }
    })

    // 如果是相关性排序，重新按相关性评分排序
    if (sort === 'relevance' && query.trim()) {
      enhancedCompanies.sort((a, b) => b.relevanceScore - a.relevanceScore)
    }

    // 统计信息
    const stats = {
      total,
      verified: companies.filter(c => c.isVerified).length,
      hasRatings: companies.filter(c => (c.totalRatings || 0) > 0).length,
      hasSalaries: companies.filter(c => (c.totalSalaries || 0) > 0).length,
      industries: [...new Set(companies.map(c => c.industry).filter(Boolean))],
      sizes: [...new Set(companies.map(c => c.size).filter(Boolean))],
    }

    // 搜索建议（基于现有数据）
    const suggestions = await generateSearchSuggestions(query)

    return NextResponse.json({
      success: true,
      message: `找到 ${total} 家企业`,
      data: enhancedCompanies,
      meta: {
        query: {
          text: query,
          industry,
          size,
          location,
          verified,
          minRating,
          hasReviews,
          hasSalaries,
          sort,
        },
        pagination: {
          page,
          limit,
          total,
          totalPages: Math.ceil(total / limit),
        },
        stats,
        suggestions,
        timestamp: new Date().toISOString(),
        version: '1.0',
      },
    })
  } catch (error) {
    console.error('企业搜索失败:', error)
    return NextResponse.json(
      {
        success: false,
        message: '搜索失败',
        error: {
          code: 'SEARCH_ERROR',
          message: error instanceof Error ? error.message : '未知错误',
        },
      },
      { status: 500 }
    )
  }
}

/**
 * 获取企业规模显示名称
 */
function getSizeDisplay(size: string | null): string | null {
  const sizeMap: Record<string, string> = {
    STARTUP: '初创公司',
    SMALL: '小型企业',
    MEDIUM: '中型企业',
    LARGE: '大型企业',
    ENTERPRISE: '跨国企业',
  }
  return size ? sizeMap[size] || size : null
}

/**
 * 生成搜索建议
 */
async function generateSearchSuggestions(query: string) {
  try {
    const suggestions: any = {
      companies: [],
      industries: [],
      relatedTerms: [],
    }

    // 企业名称建议（模糊匹配）
    if (query.trim()) {
      const companySuggestions = await prisma.company.findMany({
        where: {
          isActive: true,
          name: {
            contains: query,
            mode: 'insensitive',
          },
        },
        select: {
          name: true,
          industry: true,
        },
        take: 5,
      })
      suggestions.companies = companySuggestions.map(c => c.name)
    }

    // 行业建议
    const industryData = await prisma.company.groupBy({
      by: ['industry'],
      where: {
        isActive: true,
        industry: { not: null },
      },
      _count: {
        industry: true,
      },
      orderBy: {
        _count: {
          industry: 'desc',
        },
      },
      take: 10,
    })

    suggestions.industries = industryData.map(item => ({
      name: item.industry,
      count: item._count.industry,
    }))

    return suggestions
  } catch (error) {
    console.error('生成搜索建议失败:', error)
    return { companies: [], industries: [], relatedTerms: [] }
  }
}
