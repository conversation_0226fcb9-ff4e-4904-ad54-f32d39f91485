import type { Metadata } from 'next'
import { Inter } from 'next/font/google'
import './globals.css'
import { Providers } from '@/components/providers'
import { Toaster } from '@/components/ui/toaster'

const inter = Inter({ subsets: ['latin'] })

export const metadata: Metadata = {
  title: 'WorkMates - 职场社区',
  description: '专为打工人打造的职场信息分享与交流社区',
  keywords: ['职场', '求职', '企业评价', '薪资', '面经'],
  authors: [{ name: 'WorkMates Team' }],
  openGraph: {
    title: 'WorkMates - 职场社区',
    description: '专为打工人打造的职场信息分享与交流社区',
    type: 'website',
    locale: 'zh_CN',
  },
}

/**
 * 根布局组件
 * 应用于所有页面的基础布局
 */
export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html lang="zh-CN" suppressHydrationWarning>
      <body className={inter.className}>
        <Providers>
          {children}
          <Toaster />
        </Providers>
      </body>
    </html>
  )
}
