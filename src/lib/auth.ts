import { prisma } from '@/lib/prisma'
import { PrismaAdapter } from '@auth/prisma-adapter'
import bcrypt from 'bcryptjs'
import NextAuth from 'next-auth'
import CredentialsProvider from 'next-auth/providers/credentials'
import GoogleProvider from 'next-auth/providers/google'

export const { handlers, auth, signIn, signOut } = NextAuth({
  adapter: PrismaAdapter(prisma),
  secret: process.env.NEXTAUTH_SECRET,
  trustHost: true,
  providers: [
    // Google OAuth提供商
    GoogleProvider({
      clientId: process.env.GOOGLE_CLIENT_ID!,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,
      // 请求的用户信息范围
      authorization: {
        params: {
          scope: 'openid email profile',
          prompt: 'consent',
          access_type: 'offline',
          response_type: 'code',
        },
      },
    }),

    // 邮箱密码提供商
    CredentialsProvider({
      name: 'credentials',
      credentials: {
        email: { label: 'Email', type: 'email' },
        password: { label: 'Password', type: 'password' },
      },
      async authorize(credentials) {
        if (!credentials?.email || !credentials?.password) {
          return null
        }

        const user = await prisma.user.findUnique({
          where: { email: String(credentials.email) },
        })

        if (!user || !user.password) {
          return null
        }

        const isPasswordValid = await bcrypt.compare(
          String(credentials.password),
          String(user.password)
        )

        if (!isPasswordValid) {
          return null
        }

        return {
          id: user.id,
          email: user.email,
          name: user.name || null,
          image: null, // 根据数据库模型，暂时设为null
        }
      },
    }),
  ],

  callbacks: {
    // 账号关联回调
    async signIn({ user, account, profile }: any) {
      if (account?.provider === 'google') {
        return await handleGoogleSignIn(user, account, profile)
      }
      return true
    },

    // JWT回调
    async jwt({ token, user }: any) {
      if (user) {
        token.sub = user.id
      }
      return token
    },

    // 会话回调
    async session({ session, token }: any) {
      if (token.sub && session.user) {
        session.user.id = token.sub
      }
      return session
    },
  },

  pages: {
    signIn: '/auth/login',
  },

  session: {
    strategy: 'jwt',
  },
})

// Google登录处理函数 - 智能账号关联逻辑
async function handleGoogleSignIn(user: any, account: any, profile: any) {
  try {
    const email = profile?.email || user.email

    if (!email) {
      return false
    }

    // 查找现有用户
    const existingUser = await prisma.user.findUnique({
      where: { email },
      include: { accounts: true },
    })

    if (existingUser) {
      // 检查是否已有Google账号关联
      const hasGoogleAccount = existingUser.accounts.some(
        (acc: any) => acc.provider === 'google'
      )

      if (!hasGoogleAccount) {
        // 如果没有Google关联，则添加关联
        await prisma.account.create({
          data: {
            userId: existingUser.id,
            type: account.type,
            provider: account.provider,
            providerAccountId: account.providerAccountId,
            refresh_token: account.refresh_token || null,
            access_token: account.access_token || null,
            expires_at: account.expires_at || null,
            token_type: account.token_type || null,
            scope: account.scope || null,
            id_token: account.id_token || null,
          },
        })

        // 更新用户验证状态
        if (!existingUser.isVerified) {
          await prisma.user.update({
            where: { id: existingUser.id },
            data: {
              isVerified: true,
            },
          })
        }
      }
    }

    return true
  } catch (error) {
    console.error('Google sign-in error:', error)
    return false
  }
}
