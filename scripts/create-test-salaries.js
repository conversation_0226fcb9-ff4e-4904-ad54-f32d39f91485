require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function createTestSalaries() {
  console.log('Creating test salary data...\n')

  try {
    // 1. 获取用户和公司数据
    console.log('1. 获取用户和公司信息...')

    const { data: users, error: userError } = await supabase
      .from('users')
      .select('id, name, position')
      .limit(3)

    const { data: companies, error: companyError } = await supabase
      .from('companies')
      .select('id, name')
      .limit(3)

    if (userError || !users?.length) {
      console.log('❌ 无法获取用户信息:', userError?.message)
      return
    }

    if (companyError || !companies?.length) {
      console.log('❌ 无法获取公司信息:', companyError?.message)
      return
    }

    console.log(
      '✅ 找到用户:',
      users.map(u => u.name)
    )
    console.log(
      '✅ 找到公司:',
      companies.map(c => c.name)
    )

    // 2. 创建测试薪资数据
    console.log('\n2. 创建测试薪资数据...')

    const testSalaries = [
      {
        authorId: users[0].id,
        companyId: companies[0].id,
        position: '前端工程师',
        level: 'P6',
        department: '技术部',
        workLocation: '深圳',
        workType: '全职',
        experience: 3,
        education: '本科',
        baseSalary: 25000,
        bonus: 60000,
        stockOptions: 50000,
        benefits: 8000,
        totalSalary: 143000,
        currency: 'CNY',
        salaryYear: 2024,
        notes: '包含13薪和年终奖，股票期权按4年行权计算',
        tags: ['技术岗', '互联网', '大厂'],
        isVerified: false,
        isActive: true,
        isAnonymous: true,
      },
      {
        authorId: users[1].id,
        companyId: companies[0].id,
        position: '后端工程师',
        level: 'P7',
        department: '技术部',
        workLocation: '杭州',
        workType: '全职',
        experience: 5,
        education: '硕士',
        baseSalary: 35000,
        bonus: 100000,
        stockOptions: 80000,
        benefits: 12000,
        totalSalary: 227000,
        currency: 'CNY',
        salaryYear: 2024,
        notes: '薪资水平在行业中上游，工作强度较大',
        tags: ['技术岗', '后端开发', '高级工程师'],
        isVerified: true,
        isActive: true,
        isAnonymous: false,
      },
      {
        authorId: users[2].id,
        companyId: companies[1].id,
        position: '产品经理',
        level: 'P8',
        department: '产品部',
        workLocation: '北京',
        workType: '全职',
        experience: 6,
        education: '本科',
        baseSalary: 40000,
        bonus: 120000,
        stockOptions: 100000,
        benefits: 15000,
        totalSalary: 275000,
        currency: 'CNY',
        salaryYear: 2024,
        notes: '产品岗位薪资不错，有较好的晋升空间',
        tags: ['产品岗', '管理', '高级产品经理'],
        isVerified: false,
        isActive: true,
        isAnonymous: false,
      },
      {
        authorId: users[0].id,
        companyId: companies[2].id,
        position: '前端工程师',
        level: 'T5',
        department: '搜索技术部',
        workLocation: '北京',
        workType: '全职',
        experience: 2,
        education: '本科',
        baseSalary: 22000,
        bonus: 50000,
        stockOptions: 30000,
        benefits: 6000,
        totalSalary: 108000,
        currency: 'CNY',
        salaryYear: 2023,
        notes: '技术栈相对传统，但稳定性较好',
        tags: ['技术岗', '搜索', '前端'],
        isVerified: true,
        isActive: true,
        isAnonymous: true,
      },
    ]

    // 3. 批量插入薪资数据
    const { data: insertedSalaries, error: insertError } = await supabase
      .from('salaries')
      .insert(testSalaries)
      .select(
        'id, position, level, totalSalary, baseSalary, bonus, stockOptions, benefits'
      )

    if (insertError) {
      console.log('❌ 插入薪资数据失败:', insertError.message)
      return
    }

    console.log('✅ 成功创建薪资数据:')
    insertedSalaries.forEach((salary, index) => {
      console.log(
        `   ${index + 1}. ${salary.position} (${salary.level || '未知级别'})`
      )
      console.log(`      - 总薪资: ¥${salary.totalSalary.toLocaleString()}`)
      console.log(`      - 基础薪资: ¥${salary.baseSalary.toLocaleString()}`)
      console.log(`      - 年终奖: ¥${salary.bonus.toLocaleString()}`)
      console.log(`      - 股票期权: ¥${salary.stockOptions.toLocaleString()}`)
      console.log(`      - 福利补贴: ¥${salary.benefits.toLocaleString()}`)
      console.log('      ---')
    })

    // 4. 验证字段名正确性
    console.log('\n3. 验证薪资数据字段...')
    const { data: verifyData, error: verifyError } = await supabase
      .from('salaries')
      .select(
        `
        id,
        position,
        level,
        department,
        workLocation,
        workType,
        experience,
        education,
        baseSalary,
        bonus,
        stockOptions,
        benefits,
        totalSalary,
        currency,
        salaryYear,
        notes,
        tags,
        isVerified,
        isActive,
        isAnonymous,
        createdAt
      `
      )
      .in(
        'id',
        insertedSalaries.map(s => s.id)
      )

    if (verifyError) {
      console.log('❌ 验证数据失败:', verifyError.message)
      return
    }

    console.log('✅ 所有薪资字段验证成功:')
    console.log('   字段列表:', Object.keys(verifyData[0] || {}))

    // 5. 计算薪资统计
    console.log('\n4. 计算薪资统计...')
    if (verifyData.length > 0) {
      const stats = {
        totalCount: verifyData.length,
        averageTotal: (
          verifyData.reduce((sum, s) => sum + Number(s.totalSalary), 0) /
          verifyData.length
        ).toFixed(0),
        averageBase: (
          verifyData.reduce((sum, s) => sum + Number(s.baseSalary), 0) /
          verifyData.length
        ).toFixed(0),
        averageBonus: (
          verifyData.reduce((sum, s) => sum + Number(s.bonus), 0) /
          verifyData.length
        ).toFixed(0),
        averageExperience: (
          verifyData.reduce((sum, s) => sum + (s.experience || 0), 0) /
          verifyData.length
        ).toFixed(1),
        minSalary: Math.min(...verifyData.map(s => Number(s.totalSalary))),
        maxSalary: Math.max(...verifyData.map(s => Number(s.totalSalary))),
      }

      console.log('✅ 薪资统计结果:')
      console.log(`   - 总薪资记录: ${stats.totalCount}`)
      console.log(
        `   - 平均总薪资: ¥${Number(stats.averageTotal).toLocaleString()}`
      )
      console.log(
        `   - 平均基础薪资: ¥${Number(stats.averageBase).toLocaleString()}`
      )
      console.log(
        `   - 平均年终奖: ¥${Number(stats.averageBonus).toLocaleString()}`
      )
      console.log(`   - 平均工作经验: ${stats.averageExperience} 年`)
      console.log(
        `   - 薪资范围: ¥${stats.minSalary.toLocaleString()} - ¥${stats.maxSalary.toLocaleString()}`
      )
    }

    console.log('\n🎉 薪资测试数据创建成功！所有字段名验证正确。')
  } catch (error) {
    console.error('❌ 创建薪资测试数据失败:', error.message)
  }
}

createTestSalaries()
