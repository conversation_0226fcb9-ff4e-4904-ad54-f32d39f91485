// test-supabase-client.js
const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

async function testSupabaseClient() {
  console.log('开始测试Supabase客户端连接...')
  console.log('Supabase URL:', supabaseUrl ? '已设置' : '未设置')
  console.log('Supabase Key:', supabaseKey ? '已设置' : '未设置')
  
  if (!supabaseUrl || !supabaseKey) {
    console.error('❌ Supabase配置不完整')
    return
  }
  
  try {
    const supabase = createClient(supabaseUrl, supabaseKey)
    
    // 测试连接
    console.log('\n1. 测试Supabase连接...')
    const { data, error } = await supabase
      .from('users')
      .select('count')
      .limit(1)
    
    if (error) {
      console.error('❌ Supabase查询失败:', error.message)
    } else {
      console.log('✅ Supabase连接成功')
    }
    
    // 测试表列表
    console.log('\n2. 获取表信息...')
    const { data: tables, error: tablesError } = await supabase
      .rpc('get_table_list')
      .select()
    
    if (tablesError) {
      console.log('⚠️ 无法获取表列表 (这是正常的，如果没有自定义函数)')
    } else {
      console.log('✅ 表列表:', tables)
    }
    
  } catch (error) {
    console.error('❌ Supabase客户端测试失败:', error.message)
  }
}

testSupabaseClient()
