#!/usr/bin/env node

/**
 * Google OAuth 配置测试脚本
 * 用于验证环境变量是否正确配置
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Google OAuth 配置检查');
console.log('=========================\n');

// 检查 .env.local 文件是否存在
const envPath = path.join(process.cwd(), '.env.local');
const envExists = fs.existsSync(envPath);

console.log('📁 环境文件检查:');
console.log(`   .env.local: ${envExists ? '✅ 存在' : '❌ 不存在'}`);

if (!envExists) {
  console.log('\n⚠️  请先运行 node scripts/setup-google-oauth.js 创建环境文件');
  process.exit(1);
}

// 读取环境变量
require('dotenv').config({ path: envPath });

console.log('\n🔐 认证配置检查:');
console.log(`   GOOGLE_CLIENT_ID: ${process.env.GOOGLE_CLIENT_ID ? '✅ 已设置' : '❌ 未设置'}`);
console.log(`   GOOGLE_CLIENT_SECRET: ${process.env.GOOGLE_CLIENT_SECRET ? '✅ 已设置' : '❌ 未设置'}`);
console.log(`   NEXTAUTH_URL: ${process.env.NEXTAUTH_URL || '❌ 未设置'}`);
console.log(`   NEXTAUTH_SECRET: ${process.env.NEXTAUTH_SECRET ? '✅ 已设置' : '❌ 未设置'}`);

console.log('\n🗄️  数据库配置检查:');
console.log(`   DATABASE_URL: ${process.env.DATABASE_URL ? '✅ 已设置' : '❌ 未设置'}`);

// 检查 Google OAuth 凭据格式
if (process.env.GOOGLE_CLIENT_ID) {
  const clientIdPattern = /^\d+-[a-z0-9]+\.apps\.googleusercontent\.com$/;
  const isValidClientId = clientIdPattern.test(process.env.GOOGLE_CLIENT_ID);
  console.log(`\n🔍 Google Client ID 格式: ${isValidClientId ? '✅ 有效' : '❌ 无效'}`);
  
  if (!isValidClientId) {
    console.log('   ⚠️  Client ID 格式应该类似: 123456789-abcdefg.apps.googleusercontent.com');
  }
}

if (process.env.GOOGLE_CLIENT_SECRET) {
  const secretPattern = /^GOCSPX-/;
  const isValidSecret = secretPattern.test(process.env.GOOGLE_CLIENT_SECRET);
  console.log(`🔍 Google Client Secret 格式: ${isValidSecret ? '✅ 有效' : '❌ 无效'}`);
  
  if (!isValidSecret) {
    console.log('   ⚠️  Client Secret 格式应该以 GOCSPX- 开头');
  }
}

// 检查必要的依赖是否安装
console.log('\n📦 依赖检查:');
try {
  require.resolve('next-auth');
  console.log('   next-auth: ✅ 已安装');
} catch {
  console.log('   next-auth: ❌ 未安装');
}

try {
  require.resolve('@auth/prisma-adapter');
  console.log('   @auth/prisma-adapter: ✅ 已安装');
} catch {
  console.log('   @auth/prisma-adapter: ❌ 未安装');
}

try {
  require.resolve('bcryptjs');
  console.log('   bcryptjs: ✅ 已安装');
} catch {
  console.log('   bcryptjs: ❌ 未安装');
}

// 提供下一步建议
console.log('\n📋 下一步:');
if (envExists && process.env.GOOGLE_CLIENT_ID && process.env.GOOGLE_CLIENT_SECRET) {
  console.log('   ✅ 配置看起来正确！可以启动开发服务器测试登录功能。');
  console.log('   运行: npm run dev');
} else {
  console.log('   ❌ 请先完成以下步骤:');
  if (!envExists) {
    console.log('   1. 运行 node scripts/setup-google-oauth.js 创建环境文件');
  }
  if (!process.env.GOOGLE_CLIENT_ID || !process.env.GOOGLE_CLIENT_SECRET) {
    console.log('   2. 在 .env.local 中设置 Google OAuth 凭据');
  }
  if (!process.env.DATABASE_URL) {
    console.log('   3. 在 .env.local 中设置数据库连接字符串');
  }
}

console.log('\n✨ 检查完成！'); 