require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function createTestComments() {
  console.log('Creating test comments data...\n')

  try {
    // 1. 获取用户和帖子数据
    console.log('1. 获取用户和帖子信息...')

    const { data: users, error: userError } = await supabase
      .from('users')
      .select('id, name, position')
      .limit(3)

    const { data: posts, error: postError } = await supabase
      .from('posts')
      .select('id, title, type')
      .limit(3)

    if (userError || !users?.length) {
      console.log('❌ 无法获取用户信息:', userError?.message)
      return
    }

    if (postError || !posts?.length) {
      console.log('❌ 无法获取帖子信息:', postError?.message)
      return
    }

    console.log(
      '✅ 找到用户:',
      users.map(u => u.name)
    )
    console.log(
      '✅ 找到帖子:',
      posts.map(p => `[${p.type}] ${p.title.substring(0, 20)}...`)
    )

    // 2. 创建顶级评论数据
    console.log('\n2. 创建顶级评论数据...')

    const topLevelComments = [
      {
        content:
          '非常有用的分享！我也在腾讯工作过，确实如你所说，技术氛围很好，同事们都很专业。加班虽然有，但相比其他大厂已经算是比较人性化的了。',
        postId: posts[0].id,
        authorId: users[1].id,
        parentId: null,
        isAnonymous: false,
        isDeleted: false,
        likeCount: 12,
        replyCount: 0, // 稍后会更新
        createdAt: new Date(),
      },
      {
        content:
          '想了解一下腾讯的晋升机制如何？工作半年有机会升级吗？还有股票期权的政策是怎样的？',
        postId: posts[0].id,
        authorId: users[2].id,
        parentId: null,
        isAnonymous: false,
        isDeleted: false,
        likeCount: 8,
        replyCount: 0,
        createdAt: new Date(),
      },
      {
        content:
          '关于阿里P7面试，我去年刚面过，可以分享一些经验：\n1. 算法确实很重要，至少要能熟练写出中等难度的题\n2. 系统设计重点考察架构思维，建议多看看高并发、分布式相关的内容\n3. 项目经验要能深入讲解技术选型和架构演进过程\n\n总的来说准备充分的话问题不大，加油！',
        postId: posts[1].id,
        authorId: users[0].id,
        parentId: null,
        isAnonymous: false,
        isDeleted: false,
        likeCount: 25,
        replyCount: 0,
        createdAt: new Date(),
      },
      {
        content:
          '匿名补充一个：阿里的面试官都很专业，会根据你的背景调整问题难度。建议提前准备一些开放性问题的回答，比如"如何设计一个类似微博的系统"这种。',
        postId: posts[1].id,
        authorId: users[2].id,
        parentId: null,
        isAnonymous: true, // 匿名评论
        isDeleted: false,
        likeCount: 15,
        replyCount: 0,
        createdAt: new Date(),
      },
      {
        content:
          '前端技术发展太快了，感觉学不过来。不过Next.js确实是个好方向，全栈开发越来越重要了。对于AI辅助开发，现在GitHub Copilot用得挺多的，确实能提高效率。',
        postId: posts[2].id,
        authorId: users[1].id,
        parentId: null,
        isAnonymous: false,
        isDeleted: false,
        likeCount: 18,
        replyCount: 0,
        createdAt: new Date(),
      },
      {
        content:
          '关于35岁程序员这个话题，我觉得关键是要形成自己的技术壁垒。不能只是单纯写代码，要么往架构方向发展，要么往管理方向转，要么考虑创业。年龄其实不是最大的问题，技能的更新才是。',
        postId: posts[2].id,
        authorId: users[0].id,
        parentId: null,
        isAnonymous: false,
        isDeleted: false,
        likeCount: 32,
        replyCount: 0,
        createdAt: new Date(),
      },
    ]

    // 插入顶级评论
    const { data: insertedComments, error: insertError } = await supabase
      .from('comments')
      .insert(topLevelComments)
      .select(
        'id, content, postId, authorId, isAnonymous, likeCount, createdAt'
      )

    if (insertError) {
      console.log('❌ 插入顶级评论失败:', insertError.message)
      return
    }

    console.log('✅ 成功创建顶级评论:')
    insertedComments.forEach((comment, index) => {
      console.log(`   ${index + 1}. ${comment.content.substring(0, 50)}...`)
      console.log(`      - 👍 ${comment.likeCount} 赞`)
      console.log(`      - 匿名: ${comment.isAnonymous ? '是' : '否'}`)
      console.log('      ---')
    })

    // 3. 创建回复评论（嵌套结构）
    console.log('\n3. 创建回复评论（嵌套结构）...')

    const replyComments = [
      {
        content:
          '感谢分享！想问一下腾讯的技术栈主要用什么？我看你提到React，是不是内部统一用React技术栈？',
        postId: posts[0].id,
        authorId: users[2].id,
        parentId: insertedComments[0].id, // 回复第一条评论
        isAnonymous: false,
        isDeleted: false,
        likeCount: 5,
        replyCount: 0,
        createdAt: new Date(),
      },
      {
        content:
          '主要是React + TypeScript，微前端用的是腾讯自研的框架。后端主要是Go和Node.js，数据库用MySQL和Redis比较多。',
        postId: posts[0].id,
        authorId: users[0].id, // 原评论作者回复
        parentId: insertedComments[0].id,
        isAnonymous: false,
        isDeleted: false,
        likeCount: 8,
        replyCount: 0,
        createdAt: new Date(),
      },
      {
        content:
          '非常感谢！这些信息对我很有帮助。算法题一般什么难度？Leetcode中等题的水平够吗？',
        postId: posts[1].id,
        authorId: users[1].id,
        parentId: insertedComments[2].id, // 回复关于阿里面试的评论
        isAnonymous: false,
        isDeleted: false,
        likeCount: 3,
        replyCount: 0,
        createdAt: new Date(),
      },
      {
        content:
          '中等题肯定要熟练，困难题至少要会思路。建议多练练动态规划、图论、字符串算法这些。面试时主要看思路清晰度和代码质量。',
        postId: posts[1].id,
        authorId: users[0].id,
        parentId: insertedComments[2].id,
        isAnonymous: false,
        isDeleted: false,
        likeCount: 12,
        replyCount: 0,
        createdAt: new Date(),
      },
      {
        content:
          '同意！我觉得技术人员最重要的是保持学习能力。35岁不是终点，而是新的起点。很多人这个年龄正是经验和技术积累的黄金期。',
        postId: posts[2].id,
        authorId: users[2].id,
        parentId: insertedComments[5].id, // 回复关于35岁程序员的评论
        isAnonymous: false,
        isDeleted: false,
        likeCount: 15,
        replyCount: 0,
        createdAt: new Date(),
      },
      {
        content:
          '对，关键是要有危机意识，不能安于现状。我身边有些40多岁的架构师，技术能力和学习能力都很强，反而比年轻人更受欢迎。',
        postId: posts[2].id,
        authorId: users[1].id,
        parentId: insertedComments[5].id,
        isAnonymous: false,
        isDeleted: false,
        likeCount: 9,
        replyCount: 0,
        createdAt: new Date(),
      },
    ]

    // 插入回复评论
    const { data: insertedReplies, error: replyError } = await supabase
      .from('comments')
      .insert(replyComments)
      .select('id, content, parentId, isAnonymous, likeCount, createdAt')

    if (replyError) {
      console.log('❌ 插入回复评论失败:', replyError.message)
      return
    }

    console.log('✅ 成功创建回复评论:')
    insertedReplies.forEach((reply, index) => {
      console.log(`   ${index + 1}. ${reply.content.substring(0, 40)}...`)
      console.log(`      - 回复: ${reply.parentId}`)
      console.log(`      - 👍 ${reply.likeCount} 赞`)
      console.log('      ---')
    })

    // 4. 更新父评论的回复数
    console.log('\n4. 更新父评论的回复数...')

    // 统计每个父评论的回复数
    const replyCountMap = {}
    insertedReplies.forEach(reply => {
      replyCountMap[reply.parentId] = (replyCountMap[reply.parentId] || 0) + 1
    })

    // 批量更新父评论的replyCount
    for (const [parentId, count] of Object.entries(replyCountMap)) {
      await supabase
        .from('comments')
        .update({ replyCount: count })
        .eq('id', parentId)
    }

    console.log('✅ 父评论回复数更新完成')

    // 5. 更新帖子的评论数
    console.log('\n5. 更新帖子的评论数...')

    // 统计每个帖子的评论数（包括回复）
    const allComments = [...insertedComments, ...insertedReplies]
    const postCommentCountMap = {}
    allComments.forEach(comment => {
      postCommentCountMap[comment.postId] =
        (postCommentCountMap[comment.postId] || 0) + 1
    })

    // 批量更新帖子的commentCount
    for (const [postId, count] of Object.entries(postCommentCountMap)) {
      await supabase
        .from('posts')
        .update({ commentCount: count })
        .eq('id', postId)
    }

    console.log('✅ 帖子评论数更新完成')

    // 6. 验证评论数据结构
    console.log('\n6. 验证评论数据结构...')
    const { data: verifyData, error: verifyError } = await supabase
      .from('comments')
      .select(
        `
        id,
        content,
        postId,
        authorId,
        parentId,
        isAnonymous,
        isDeleted,
        likeCount,
        replyCount,
        createdAt,
        updatedAt
      `
      )
      .in('id', [
        ...insertedComments.map(c => c.id),
        ...insertedReplies.map(r => r.id),
      ])

    if (verifyError) {
      console.log('❌ 验证数据失败:', verifyError.message)
      return
    }

    console.log('✅ 所有评论字段验证成功:')
    console.log('   字段列表:', Object.keys(verifyData[0] || {}))

    // 7. 计算评论统计
    console.log('\n7. 计算评论统计...')
    if (verifyData.length > 0) {
      const stats = {
        totalComments: verifyData.length,
        topLevelComments: verifyData.filter(c => !c.parentId).length,
        replyComments: verifyData.filter(c => c.parentId).length,
        averageLikes: (
          verifyData.reduce((sum, c) => sum + (c.likeCount || 0), 0) /
          verifyData.length
        ).toFixed(1),
        anonymousComments: verifyData.filter(c => c.isAnonymous).length,
        commentsWithReplies: verifyData.filter(c => (c.replyCount || 0) > 0)
          .length,
      }

      console.log('✅ 评论统计结果:')
      console.log(`   - 总评论数: ${stats.totalComments}`)
      console.log(`   - 顶级评论: ${stats.topLevelComments}`)
      console.log(`   - 回复评论: ${stats.replyComments}`)
      console.log(`   - 平均点赞数: ${stats.averageLikes}`)
      console.log(`   - 匿名评论: ${stats.anonymousComments}`)
      console.log(`   - 有回复的评论: ${stats.commentsWithReplies}`)

      // 8. 验证嵌套结构
      console.log('\n8. 验证嵌套结构...')
      const nestingExamples = verifyData.filter(c => c.parentId).slice(0, 3)
      console.log('✅ 嵌套回复示例:')
      for (const reply of nestingExamples) {
        const parent = verifyData.find(c => c.id === reply.parentId)
        console.log(`   回复: "${reply.content.substring(0, 30)}..."`)
        console.log(`   父评论: "${parent?.content.substring(0, 30)}..."`)
        console.log('   ---')
      }
    }

    console.log('\n🎉 评论测试数据创建成功！嵌套回复功能验证正确。')
  } catch (error) {
    console.error('❌ 创建评论测试数据失败:', error.message)
  }
}

createTestComments()
