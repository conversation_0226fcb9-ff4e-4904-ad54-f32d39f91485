#!/usr/bin/env node

/**
 * WorkMates Google OAuth 配置设置脚本
 * 运行此脚本将帮助您设置 Google OAuth 环境变量
 */

const fs = require('fs');
const path = require('path');
const readline = require('readline');

const rl = readline.createInterface({
  input: process.stdin,
  output: process.stdout
});

// Google OAuth 凭据（已提供）
const GOOGLE_CLIENT_ID = '232576410919-16b9kb88t7vba23flua5luhib18kof0l.apps.googleusercontent.com';
const GOOGLE_CLIENT_SECRET = '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';

// 生成安全的 NEXTAUTH_SECRET
function generateSecret() {
  return require('crypto').randomBytes(32).toString('base64');
}

// 环境变量模板
const envTemplate = `# ==========================================
# WorkMates 项目环境变量配置
# ==========================================

# Supabase 数据库配置
DATABASE_URL="postgresql://postgres.zfctpeukxaxftfsmpqgp:[YOUR-PASSWORD]@aws-0-ap-northeast-1.pooler.supabase.com:6543/postgres"
DIRECT_URL="postgresql://postgres.zfctpeukxaxftfsmpqgp:[YOUR-PASSWORD]@aws-0-ap-northeast-1.pooler.supabase.com:6543/postgres"

# Supabase API 配置
NEXT_PUBLIC_SUPABASE_URL="https://zfctpeukxaxftfsmpqgp.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6InpmY3RwZXVreGF4ZnRmc21wcWdwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTE3NjUyMTYsImV4cCI6MjA2NzM0MTIxNn0.CuPOCJ-RkxXNQb3A7yeTuS0WLtXQ6tF7gkUUEkO3Dx0"
SUPABASE_SERVICE_ROLE_KEY="[从Supabase Dashboard获取]"

# NextAuth.js 配置
NEXTAUTH_SECRET="${generateSecret()}"
NEXTAUTH_URL="http://localhost:3000"

# Google OAuth 配置
GOOGLE_CLIENT_ID="${GOOGLE_CLIENT_ID}"
GOOGLE_CLIENT_SECRET="${GOOGLE_CLIENT_SECRET}"

# Email 配置 (可选)
SMTP_HOST=""
SMTP_PORT=""
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM=""

# 文件上传配置
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE="5242880" # 5MB
SUPABASE_STORAGE_BUCKET="experience-files"

# App Configuration
APP_NAME="WorkMates"
APP_URL="http://localhost:3000"

# Admin Configuration
ADMIN_EMAIL="<EMAIL>"

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE="60"

# Development
NODE_ENV="development"
`;

// 主函数
async function main() {
  console.log('🚀 WorkMates Google OAuth 配置设置');
  console.log('=====================================\n');

  const envPath = path.join(process.cwd(), '.env.local');

  // 检查是否已存在 .env.local
  if (fs.existsSync(envPath)) {
    console.log('⚠️  检测到已存在 .env.local 文件');
    
    rl.question('是否要覆盖现有的 .env.local 文件？(y/N): ', (answer) => {
      if (answer.toLowerCase() !== 'y') {
        console.log('✅ 保留现有配置文件');
        console.log('\n请手动在 .env.local 中添加以下配置：');
        console.log(`GOOGLE_CLIENT_ID="${GOOGLE_CLIENT_ID}"`);
        console.log(`GOOGLE_CLIENT_SECRET="${GOOGLE_CLIENT_SECRET}"`);
        rl.close();
        return;
      }
      
      writeEnvFile(envPath);
      rl.close();
    });
  } else {
    writeEnvFile(envPath);
    rl.close();
  }
}

function writeEnvFile(envPath) {
  try {
    fs.writeFileSync(envPath, envTemplate);
    console.log('✅ 成功创建 .env.local 文件！');
    console.log('\n📝 已配置的 Google OAuth 凭据：');
    console.log(`   Client ID: ${GOOGLE_CLIENT_ID}`);
    console.log(`   Client Secret: ${GOOGLE_CLIENT_SECRET.substring(0, 10)}...`);
    console.log('\n⚠️  重要提醒：');
    console.log('   1. 请更新 DATABASE_URL 中的 [YOUR-PASSWORD] 为实际的数据库密码');
    console.log('   2. 请从 Supabase Dashboard 获取并更新 SUPABASE_SERVICE_ROLE_KEY');
    console.log('   3. 生产环境请更新 NEXTAUTH_URL 和 APP_URL');
    console.log('\n✨ 配置完成！现在可以开始开发认证功能了。');
  } catch (error) {
    console.error('❌ 创建 .env.local 文件失败：', error.message);
  }
}

// 运行主函数
main().catch(console.error); 