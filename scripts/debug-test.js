#!/usr/bin/env node

/**
 * WorkMates 调试测试脚本
 * 用于验证调试环境是否正常工作
 */

console.log('🚀 WorkMates 调试测试')
console.log('====================')

// 测试1: 基本日志输出
console.log('✅ 测试1: 基本日志输出')
console.log('这是一个普通的日志消息')
console.warn('这是一个警告消息')
console.error('这是一个错误消息（测试用）')

// 测试2: 变量调试
console.log('\n✅ 测试2: 变量调试')
const testData = {
  name: 'WorkMates',
  version: '1.0.0',
  environment: process.env.NODE_ENV || 'development',
  nodeVersion: process.version,
  platform: process.platform
}

console.log('测试数据:', testData)

// 测试3: 断点测试（在此处设置断点）
console.log('\n✅ 测试3: 断点测试')
debugger; // 在此处会触发断点

const numbers = [1, 2, 3, 4, 5]
const doubled = numbers.map(n => n * 2)
console.log('原始数组:', numbers)
console.log('翻倍后:', doubled)

// 测试4: 异步操作调试
console.log('\n✅ 测试4: 异步操作调试')
async function testAsync() {
  console.log('开始异步操作...')
  
  // 模拟异步操作
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  console.log('异步操作完成')
  return '异步结果'
}

// 测试5: 错误处理调试
console.log('\n✅ 测试5: 错误处理调试')
function testError() {
  try {
    // 故意触发错误
    throw new Error('这是一个测试错误')
  } catch (error) {
    console.error('捕获到错误:', error.message)
  }
}

// 执行测试
async function runTests() {
  try {
    const result = await testAsync()
    console.log('异步测试结果:', result)
    
    testError()
    
    console.log('\n🎉 所有测试完成!')
    console.log('如果您能看到这些消息，说明调试环境工作正常')
    
  } catch (error) {
    console.error('测试失败:', error)
  }
}

// 运行测试
runTests() 