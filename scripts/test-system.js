const http = require('http')
const https = require('https')

// 测试端点列表
const endpoints = [
  { name: '数据库测试', url: '/api/test-db', method: 'GET' },
  { name: '公司API', url: '/api/companies', method: 'GET' },
  { name: '帖子API', url: '/api/posts', method: 'GET' },
  { name: '搜索API', url: '/api/search?q=test', method: 'GET' },
  { name: '用户搜索', url: '/api/search/users?q=test', method: 'GET' },
  { name: '公司搜索', url: '/api/search/companies?q=test', method: 'GET' },
  { name: '帖子搜索', url: '/api/search/posts?q=test', method: 'GET' },
]

// 测试函数
function testEndpoint(endpoint) {
  return new Promise(resolve => {
    const options = {
      hostname: 'localhost',
      port: 3000,
      path: endpoint.url,
      method: endpoint.method,
      timeout: 5000,
    }

    const req = http.request(options, res => {
      let data = ''
      res.on('data', chunk => (data += chunk))
      res.on('end', () => {
        try {
          const jsonData = JSON.parse(data)
          resolve({
            name: endpoint.name,
            status: res.statusCode,
            success: res.statusCode === 200,
            response: jsonData.success ? '✅ 成功' : '❌ 失败',
            message: jsonData.message || '无消息',
          })
        } catch (e) {
          resolve({
            name: endpoint.name,
            status: res.statusCode,
            success: false,
            response: '❌ JSON解析失败',
            message: data.substring(0, 100),
          })
        }
      })
    })

    req.on('error', err => {
      resolve({
        name: endpoint.name,
        status: 'ERROR',
        success: false,
        response: '❌ 连接失败',
        message: err.message,
      })
    })

    req.on('timeout', () => {
      req.destroy()
      resolve({
        name: endpoint.name,
        status: 'TIMEOUT',
        success: false,
        response: '❌ 超时',
        message: '请求超时',
      })
    })

    req.end()
  })
}

// 运行测试
async function runTests() {
  console.log('🚀 开始 WorkMates 系统功能测试...\\n')

  const results = []

  for (const endpoint of endpoints) {
    console.log(`📡 测试: ${endpoint.name}`)
    const result = await testEndpoint(endpoint)
    results.push(result)
    console.log(`   状态: ${result.status} | ${result.response}`)
    console.log(`   消息: ${result.message}\\n`)
  }

  // 生成测试报告
  console.log('📊 测试报告:')
  console.log('='.repeat(60))

  const successful = results.filter(r => r.success).length
  const total = results.length

  console.log(`✅ 成功: ${successful}/${total}`)
  console.log(`❌ 失败: ${total - successful}/${total}`)
  console.log(`📈 成功率: ${((successful / total) * 100).toFixed(1)}%`)

  console.log('\\n📋 详细结果:')
  results.forEach(result => {
    const status = result.success ? '✅' : '❌'
    console.log(`${status} ${result.name.padEnd(20)} | ${result.status}`)
  })

  if (successful === total) {
    console.log('\\n🎉 所有测试通过！系统运行正常。')
  } else {
    console.log('\\n⚠️  存在失败的测试，需要修复。')
  }
}

// 执行测试
runTests().catch(console.error)
