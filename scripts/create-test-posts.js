require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function createTestPosts() {
  console.log('Creating test forum posts...\n')

  try {
    // 1. 获取用户和公司数据
    console.log('1. 获取用户和公司信息...')

    const { data: users, error: userError } = await supabase
      .from('users')
      .select('id, name, position')
      .limit(3)

    const { data: companies, error: companyError } = await supabase
      .from('companies')
      .select('id, name')
      .limit(3)

    if (userError || !users?.length) {
      console.log('❌ 无法获取用户信息:', userError?.message)
      return
    }

    if (companyError || !companies?.length) {
      console.log('❌ 无法获取公司信息:', companyError?.message)
      return
    }

    console.log(
      '✅ 找到用户:',
      users.map(u => u.name)
    )
    console.log(
      '✅ 找到公司:',
      companies.map(c => c.name)
    )

    // 2. 创建测试帖子数据
    console.log('\n2. 创建测试帖子数据...')

    const testPosts = [
      {
        title: '腾讯前端团队工作体验分享',
        content: `最近在腾讯工作了半年，想和大家分享一下真实的工作体验。

**技术栈：**
- React 18 + TypeScript
- Next.js 13 App Router  
- Tailwind CSS + shadcn/ui
- 微前端架构

**工作环境：**
腾讯的技术氛围还是很不错的，同事们都比较专业，代码review很严格，能学到很多东西。办公环境也挺好的，各种福利都有。

**加班情况：**
平时不算太累，一般晚上8点左右能走。遇到版本发布会加班到比较晚，但加班费给的还可以。

**成长机会：**
公司内部有很多技术分享，还有导师制度。项目复杂度比较高，对技术能力提升很有帮助。

总的来说，腾讯是个不错的平台，特别适合想要在大厂积累经验的同学。`,
        excerpt:
          '腾讯前端半年工作体验分享，包含技术栈、工作环境、加班情况等真实感受。',
        type: 'SHARING',
        category: '工作体验',
        tags: ['腾讯', '前端', '工作体验', 'React'],
        companyId: companies[0].id, // 腾讯
        authorId: users[0].id,
        isAnonymous: false,
        isPublished: true,
        viewCount: 156,
        likeCount: 23,
        commentCount: 8,
        shareCount: 5,
      },
      {
        title: '求助：如何准备阿里P7面试？',
        content: `有没有大佬分享一下阿里P7的面试经验？我准备了2个月，感觉还是心里没底。

**我的背景：**
- 工作4年，主要做React开发
- 之前在一家中型互联网公司
- 有一些项目架构经验

**想了解的问题：**
1. 技术面试主要考察哪些方面？
2. 系统设计题一般是什么难度？
3. 算法题会不会很难？
4. 有什么需要特别准备的吗？

感谢各位前辈指点！🙏`,
        excerpt:
          '求助阿里P7面试准备，工作4年React开发背景，想了解面试重点和准备方向。',
        type: 'QUESTION',
        category: '面试求助',
        tags: ['阿里巴巴', 'P7', '面试', '求助'],
        companyId: companies[1].id, // 阿里巴巴
        authorId: users[1].id,
        isAnonymous: false,
        isPublished: true,
        viewCount: 89,
        likeCount: 12,
        commentCount: 15,
        shareCount: 3,
      },
      {
        title: '2024年前端技术趋势预测',
        content: `年底了，总结一下今年的前端技术发展，顺便预测一下明年的趋势。

**2024年热门技术：**

1. **框架方面**
   - Next.js 14/15 App Router 成为主流
   - Remix 开始受到关注
   - Vue 3 生态逐渐完善

2. **构建工具**
   - Vite 基本替代了 Webpack
   - Turbo、Rspack 等新工具崭露头角
   - ESM 成为标准

3. **样式方案**
   - Tailwind CSS 统治地位稳固
   - CSS-in-JS 开始衰落
   - 原生 CSS 新特性增多

4. **类型安全**
   - TypeScript 使用率继续上升
   - 运行时类型检查受关注

**2025年预测：**
- AI 辅助开发工具会更加普及
- WebAssembly 应用场景扩大
- 微前端架构成熟化
- 服务端渲染回归主流

大家觉得呢？有什么不同的看法？`,
        excerpt:
          '2024年前端技术总结和2025年趋势预测，涵盖框架、构建工具、样式方案等多个方面。',
        type: 'DISCUSSION',
        category: '技术讨论',
        tags: ['前端', '技术趋势', '2024', '预测'],
        companyId: null,
        authorId: users[2].id,
        isAnonymous: false,
        isPublished: true,
        viewCount: 234,
        likeCount: 45,
        commentCount: 28,
        shareCount: 12,
      },
      {
        title: '字节跳动面试凉经（三轮技术面）',
        content: `刚面完字节，结果很遗憾，分享一下面试过程，希望对大家有帮助。

**一面（1小时）：**
- 自我介绍 + 项目介绍
- React Hooks 原理
- 浏览器渲染流程
- HTTP 缓存策略
- 手写 Promise.all
- 算法：两数之和

一面过了，感觉还挺顺利的。

**二面（1.5小时）：**
- 深入聊项目架构
- webpack 配置优化
- 前端性能优化实践
- 手写防抖节流
- 算法：LRU 缓存实现
- 系统设计：设计一个评论系统

二面有点紧张，LRU没写出来，系统设计答得不够深入。

**三面（1小时）：**
- 职业规划
- 技术管理相关
- 开放性问题较多
- 没有算法题

三面感觉聊得还可以，但估计前面的表现拉分了。

**总结：**
字节的面试确实有一定难度，特别是系统设计部分。建议大家：
1. 算法要多练，特别是中等难度的
2. 系统设计要有框架性思维
3. 项目要能讲清楚技术决策

继续努力，下次再战！💪`,
        excerpt:
          '字节跳动三轮技术面试详细过程分享，包含具体题目和面试总结建议。',
        type: 'SHARING',
        category: '面试经验',
        tags: ['字节跳动', '面试', '凉经', '前端'],
        companyId: null, // 字节跳动不在我们的测试公司中
        authorId: users[0].id,
        isAnonymous: true, // 匿名分享
        isPublished: true,
        viewCount: 178,
        likeCount: 34,
        commentCount: 19,
        shareCount: 7,
      },
      {
        title: '讨论：35岁程序员的职业出路',
        content: `看到最近很多关于35岁程序员的讨论，作为一个33岁的老程序员，想和大家探讨一下这个话题。

**现状：**
- 工作10年，目前是某公司技术负责人
- 感觉体力和学习能力确实在下降
- 新技术层出不穷，有时候跟不上节奏
- 家庭压力增大，加班越来越少

**困惑：**
1. 是否应该转向管理岗位？
2. 技术专家路线是否可行？
3. 创业是否是个选择？
4. 如何保持技术竞争力？

**大家的看法：**
- 有类似经历的朋友可以分享一下吗？
- 不同公司对高龄程序员的态度如何？
- 有什么好的转型建议？

希望能有一些建设性的讨论，而不是焦虑传播。毕竟35岁才是人生的开始！😊`,
        excerpt:
          '33岁技术负责人讨论35岁程序员职业出路，寻求转型建议和经验分享。',
        type: 'DISCUSSION',
        category: '职业发展',
        tags: ['35岁', '程序员', '职业规划', '转型'],
        companyId: null,
        authorId: users[1].id,
        isAnonymous: false,
        isPublished: true,
        viewCount: 312,
        likeCount: 67,
        commentCount: 42,
        shareCount: 18,
      },
      {
        title: '【招聘】北京某独角兽公司急招前端开发',
        content: `我们公司正在快速发展，急需优秀的前端开发工程师加入！

**公司介绍：**
- 独角兽公司，C轮融资
- 主营业务：企业服务SaaS
- 团队规模：200+ 人
- 办公地点：北京朝阳区

**岗位要求：**
- 本科及以上学历，3年以上前端开发经验
- 熟练掌握 React/Vue，了解源码原理
- 熟悉 TypeScript、Webpack 等工具
- 有大型项目架构经验优先
- 有良好的代码规范和团队协作能力

**技术栈：**
- React 18 + TypeScript
- Next.js + tRPC
- Tailwind CSS
- Prisma + PostgreSQL
- 微服务架构

**福利待遇：**
- 薪资：25K-45K * 14薪
- 股票期权
- 弹性工作时间
- 带薪年假 + 各种假期
- 团建活动丰富

**联系方式：**
感兴趣的朋友可以私信我，或者发简历到：<EMAIL>

我们团队氛围很好，技术氛围浓厚，欢迎大家加入！🚀`,
        excerpt:
          '北京独角兽公司招聘前端开发，React技术栈，25-45K薪资，提供股票期权。',
        type: 'JOB',
        category: '招聘信息',
        tags: ['招聘', '前端', '北京', 'React', '独角兽'],
        companyId: null,
        authorId: users[2].id,
        isAnonymous: false,
        isPublished: true,
        viewCount: 145,
        likeCount: 28,
        commentCount: 12,
        shareCount: 9,
      },
    ]

    // 3. 批量插入帖子数据
    const { data: insertedPosts, error: insertError } = await supabase
      .from('posts')
      .insert(testPosts)
      .select(
        'id, title, type, category, tags, viewCount, likeCount, commentCount, isAnonymous, createdAt'
      )

    if (insertError) {
      console.log('❌ 插入帖子数据失败:', insertError.message)
      return
    }

    console.log('✅ 成功创建帖子数据:')
    insertedPosts.forEach((post, index) => {
      console.log(`   ${index + 1}. [${post.type}] ${post.title}`)
      console.log(`      - 分类: ${post.category}`)
      console.log(`      - 标签: ${post.tags.join(', ')}`)
      console.log(
        `      - 统计: 👀${post.viewCount} 👍${post.likeCount} 💬${post.commentCount}`
      )
      console.log(`      - 匿名: ${post.isAnonymous ? '是' : '否'}`)
      console.log('      ---')
    })

    // 4. 验证字段名和数据结构
    console.log('\n3. 验证帖子数据字段...')
    const { data: verifyData, error: verifyError } = await supabase
      .from('posts')
      .select(
        `
        id,
        title,
        content,
        excerpt,
        type,
        category,
        tags,
        companyId,
        authorId,
        isAnonymous,
        isPublished,
        isPinned,
        isLocked,
        isDeleted,
        viewCount,
        likeCount,
        commentCount,
        shareCount,
        createdAt,
        updatedAt,
        publishedAt
      `
      )
      .in(
        'id',
        insertedPosts.map(p => p.id)
      )

    if (verifyError) {
      console.log('❌ 验证数据失败:', verifyError.message)
      return
    }

    console.log('✅ 所有帖子字段验证成功:')
    console.log('   字段列表:', Object.keys(verifyData[0] || {}))

    // 5. 计算帖子统计
    console.log('\n4. 计算帖子统计...')
    if (verifyData.length > 0) {
      const stats = {
        totalCount: verifyData.length,
        averageViews: (
          verifyData.reduce((sum, p) => sum + (p.viewCount || 0), 0) /
          verifyData.length
        ).toFixed(0),
        averageLikes: (
          verifyData.reduce((sum, p) => sum + (p.likeCount || 0), 0) /
          verifyData.length
        ).toFixed(1),
        averageComments: (
          verifyData.reduce((sum, p) => sum + (p.commentCount || 0), 0) /
          verifyData.length
        ).toFixed(1),
        typeDistribution: {},
        anonymousCount: verifyData.filter(p => p.isAnonymous).length,
        publishedCount: verifyData.filter(p => p.isPublished).length,
      }

      // 统计帖子类型分布
      verifyData.forEach(post => {
        stats.typeDistribution[post.type] =
          (stats.typeDistribution[post.type] || 0) + 1
      })

      console.log('✅ 帖子统计结果:')
      console.log(`   - 总帖子数: ${stats.totalCount}`)
      console.log(`   - 平均浏览量: ${stats.averageViews}`)
      console.log(`   - 平均点赞数: ${stats.averageLikes}`)
      console.log(`   - 平均评论数: ${stats.averageComments}`)
      console.log(`   - 已发布帖子: ${stats.publishedCount}`)
      console.log(`   - 匿名帖子: ${stats.anonymousCount}`)
      console.log('   - 类型分布:')
      Object.entries(stats.typeDistribution).forEach(([type, count]) => {
        console.log(`     * ${type}: ${count}个`)
      })
    }

    console.log('\n🎉 论坛测试数据创建成功！所有字段名验证正确。')
  } catch (error) {
    console.error('❌ 创建帖子测试数据失败:', error.message)
  }
}

createTestPosts()
