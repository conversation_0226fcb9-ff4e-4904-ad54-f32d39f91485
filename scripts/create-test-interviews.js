require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function createTestInterviews() {
  console.log('Creating test interview data...\n')

  try {
    // 1. 获取用户和公司数据
    console.log('1. 获取用户和公司信息...')

    const { data: users, error: userError } = await supabase
      .from('users')
      .select('id, name, position')
      .limit(3)

    const { data: companies, error: companyError } = await supabase
      .from('companies')
      .select('id, name')
      .limit(3)

    if (userError || !users?.length) {
      console.log('❌ 无法获取用户信息:', userError?.message)
      return
    }

    if (companyError || !companies?.length) {
      console.log('❌ 无法获取公司信息:', companyError?.message)
      return
    }

    console.log(
      '✅ 找到用户:',
      users.map(u => u.name)
    )
    console.log(
      '✅ 找到公司:',
      companies.map(c => c.name)
    )

    // 2. 创建测试面试数据
    console.log('\n2. 创建测试面试数据...')

    const testInterviews = [
      {
        authorId: users[0].id,
        companyId: companies[0].id,
        position: '前端工程师',
        department: '技术部',
        interviewType: '技术面试',
        interviewRound: 2,
        interviewDate: '2024-01-15',
        duration: 60,
        difficulty: 'MEDIUM',
        result: 'PASSED', // 注意：使用正确的枚举值
        rating: 4,
        questions: [
          'JavaScript闭包的原理是什么？',
          'React Hooks的使用场景？',
          '如何优化前端性能？',
          '你遇到过的最难的技术问题是什么？',
        ],
        experience:
          '面试官很专业，问题有一定深度但不刁钻。整体氛围轻松，会给机会解释思路。',
        tips: '建议提前准备常见的技术问题，要能举出具体的项目例子。',
        notes: '技术栈主要考察React和TypeScript，还会问一些工程化的问题。',
        isActive: true,
        isAnonymous: false,
      },
      {
        authorId: users[1].id,
        companyId: companies[0].id,
        position: '后端工程师',
        department: '技术部',
        interviewType: '算法面试',
        interviewRound: 1,
        interviewDate: '2024-01-10',
        duration: 45,
        difficulty: 'HARD',
        result: 'FAILED', // 注意：使用正确的枚举值
        rating: 2,
        questions: [
          '实现一个LRU缓存算法',
          '设计一个分布式系统的限流器',
          '如何处理大量并发请求？',
          '数据库索引的优化策略',
        ],
        experience: '算法题比较难，需要现场写代码。面试官对代码质量要求很高。',
        tips: '一定要多刷算法题，特别是动态规划和系统设计。',
        notes: '面试时间比较紧张，建议快速理清思路再开始编码。',
        isActive: true,
        isAnonymous: true,
      },
      {
        authorId: users[2].id,
        companyId: companies[1].id,
        position: '产品经理',
        department: '产品部',
        interviewType: '综合面试',
        interviewRound: 3,
        interviewDate: '2024-01-20',
        duration: 90,
        difficulty: 'MEDIUM',
        result: 'PASSED',
        rating: 5,
        questions: [
          '如何设计一个外卖App的用户体验？',
          '产品数据异常时如何分析处理？',
          '如何平衡用户需求和技术实现难度？',
          '你认为什么样的产品是成功的？',
        ],
        experience: '面试官很友善，更像是在聊天讨论。会深入挖掘你的产品思维。',
        tips: '要有自己的产品观点，能够清楚表达设计理念和决策逻辑。',
        notes: '建议准备一些经典的产品案例分析，展示自己的产品sense。',
        isActive: true,
        isAnonymous: false,
      },
      {
        authorId: users[0].id,
        companyId: companies[2].id,
        position: '前端开发',
        department: '搜索技术部',
        interviewType: '项目面试',
        interviewRound: 1,
        interviewDate: '2024-01-08',
        duration: 75,
        difficulty: 'EASY',
        result: 'PENDING',
        rating: 3,
        questions: [
          '介绍一下你最有成就感的项目',
          'Vue和React的区别是什么？',
          '如何进行前端工程化？',
          '你对我们公司有什么了解？',
        ],
        experience: '比较注重项目经验，会详细询问技术选型和实现细节。',
        tips: '准备好详细的项目介绍，包括技术架构和你的贡献。',
        notes: '面试节奏比较慢，有充分的时间表达。公司文化相对传统。',
        isActive: true,
        isAnonymous: true,
      },
    ]

    // 3. 批量插入面试数据
    const { data: insertedInterviews, error: insertError } = await supabase
      .from('interviews')
      .insert(testInterviews)
      .select(
        'id, position, difficulty, result, rating, duration, interviewRound'
      )

    if (insertError) {
      console.log('❌ 插入面试数据失败:', insertError.message)
      // 如果是枚举值问题，提供详细错误信息
      if (
        insertError.message.includes('enum') ||
        insertError.message.includes('invalid input value')
      ) {
        console.log('🔍 可能是枚举值不匹配问题：')
        console.log('   - API使用的result值: PASSED, FAILED')
        console.log('   - 数据库期望的值可能不同')
      }
      return
    }

    console.log('✅ 成功创建面试数据:')
    insertedInterviews.forEach((interview, index) => {
      console.log(
        `   ${index + 1}. ${interview.position} - 第${interview.interviewRound}轮`
      )
      console.log(`      - 难度: ${interview.difficulty}`)
      console.log(`      - 结果: ${interview.result}`)
      console.log(`      - 评分: ${interview.rating}/5`)
      console.log(`      - 时长: ${interview.duration}分钟`)
      console.log('      ---')
    })

    // 4. 验证字段名和数据结构
    console.log('\n3. 验证面试数据字段...')
    const { data: verifyData, error: verifyError } = await supabase
      .from('interviews')
      .select(
        `
        id,
        position,
        department,
        interviewType,
        interviewRound,
        interviewDate,
        duration,
        difficulty,
        result,
        rating,
        questions,
        experience,
        tips,
        notes,
        isActive,
        isAnonymous,
        createdAt
      `
      )
      .in(
        'id',
        insertedInterviews.map(i => i.id)
      )

    if (verifyError) {
      console.log('❌ 验证数据失败:', verifyError.message)
      return
    }

    console.log('✅ 所有面试字段验证成功:')
    console.log('   字段列表:', Object.keys(verifyData[0] || {}))

    // 5. 计算面试统计
    console.log('\n4. 计算面试统计...')
    if (verifyData.length > 0) {
      const stats = {
        totalCount: verifyData.length,
        averageRating: (
          verifyData.reduce((sum, i) => sum + (i.rating || 0), 0) /
          verifyData.length
        ).toFixed(1),
        averageDuration: (
          verifyData.reduce((sum, i) => sum + (i.duration || 0), 0) /
          verifyData.length
        ).toFixed(0),
        averageRounds: (
          verifyData.reduce((sum, i) => sum + (i.interviewRound || 1), 0) /
          verifyData.length
        ).toFixed(1),
        passCount: verifyData.filter(i => i.result === 'PASSED').length,
        failCount: verifyData.filter(i => i.result === 'FAILED').length,
        pendingCount: verifyData.filter(i => i.result === 'PENDING').length,
        difficultyStats: {
          EASY: verifyData.filter(i => i.difficulty === 'EASY').length,
          MEDIUM: verifyData.filter(i => i.difficulty === 'MEDIUM').length,
          HARD: verifyData.filter(i => i.difficulty === 'HARD').length,
        },
      }

      const passRate =
        stats.totalCount > 0
          ? ((stats.passCount / stats.totalCount) * 100).toFixed(0)
          : 0

      console.log('✅ 面试统计结果:')
      console.log(`   - 总面试记录: ${stats.totalCount}`)
      console.log(`   - 平均评分: ${stats.averageRating}/5`)
      console.log(`   - 平均时长: ${stats.averageDuration}分钟`)
      console.log(`   - 平均轮次: ${stats.averageRounds}轮`)
      console.log(
        `   - 通过率: ${passRate}% (${stats.passCount}/${stats.totalCount})`
      )
      console.log(
        `   - 结果分布: 通过${stats.passCount}, 失败${stats.failCount}, 待定${stats.pendingCount}`
      )
      console.log(
        `   - 难度分布: 简单${stats.difficultyStats.EASY}, 中等${stats.difficultyStats.MEDIUM}, 困难${stats.difficultyStats.HARD}`
      )
    }

    console.log('\n🎉 面试测试数据创建成功！枚举值和字段名验证正确。')
  } catch (error) {
    console.error('❌ 创建面试测试数据失败:', error.message)
  }
}

createTestInterviews()
