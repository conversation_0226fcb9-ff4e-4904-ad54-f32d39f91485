require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function checkTableStructure() {
  console.log('Checking database table structures...\n')

  // 使用information_schema查询ratings表的列信息
  const { data: columns, error } = await supabase
    .from('information_schema.columns')
    .select('column_name, data_type, is_nullable')
    .eq('table_schema', 'public')
    .eq('table_name', 'ratings')
    .order('ordinal_position')

  if (error) {
    console.error('Error fetching table structure:', error)
    // 尝试直接查询一条记录
    const { data: sampleData, error: sampleError } = await supabase
      .from('ratings')
      .select('*')
      .limit(1)

    if (!sampleError && sampleData) {
      console.log(
        'Sample ratings data columns:',
        Object.keys(sampleData[0] || {})
      )
    }
    return
  }

  if (columns && columns.length > 0) {
    console.log('Ratings table structure:')
    columns.forEach(col => {
      console.log(
        `  - ${col.column_name} (${col.data_type}) ${col.is_nullable === 'NO' ? 'NOT NULL' : ''}`
      )
    })
  } else {
    console.log('Could not fetch table structure from information_schema')

    // 尝试插入一条测试数据来确认字段名
    const testData = {
      authorId: '00000000-0000-0000-0000-000000000000',
      companyId: '00000000-0000-0000-0000-000000000001',
      overallRating: 4.5,
      workLifeBalance: 4,
      compensation: 3,
      culture: 5,
      careerGrowth: 4,
      management: 3,
      title: 'Test Rating',
      pros: 'Test',
      cons: 'Test',
      isAnonymous: true,
    }

    console.log('\nTrying to insert test data with camelCase fields...')
    const { data: insertResult, error: insertError } = await supabase
      .from('ratings')
      .insert([testData])
      .select()

    if (insertError) {
      console.log('Insert with camelCase failed:', insertError.message)

      // 尝试使用下划线命名
      const testDataSnakeCase = {
        author_id: '00000000-0000-0000-0000-000000000000',
        company_id: '00000000-0000-0000-0000-000000000001',
        overall_rating: 4.5,
        work_life_balance: 4,
        compensation: 3,
        culture: 5,
        career_growth: 4,
        management: 3,
        title: 'Test Rating',
        pros: 'Test',
        cons: 'Test',
        is_anonymous: true,
      }

      console.log('\nTrying to insert test data with snake_case fields...')
      const { data: insertResult2, error: insertError2 } = await supabase
        .from('ratings')
        .insert([testDataSnakeCase])
        .select()

      if (insertError2) {
        console.log('Insert with snake_case also failed:', insertError2.message)
      } else {
        console.log(
          'Insert with snake_case succeeded! Columns are in snake_case format.'
        )
        console.log('Inserted data:', insertResult2)

        // 删除测试数据
        if (insertResult2 && insertResult2[0]) {
          await supabase.from('ratings').delete().eq('id', insertResult2[0].id)
          console.log('Test data cleaned up.')
        }
      }
    } else {
      console.log(
        'Insert with camelCase succeeded! Columns are in camelCase format.'
      )
      console.log('Inserted data:', insertResult)

      // 删除测试数据
      if (insertResult && insertResult[0]) {
        await supabase.from('ratings').delete().eq('id', insertResult[0].id)
        console.log('Test data cleaned up.')
      }
    }
  }
}

checkTableStructure().catch(console.error)
