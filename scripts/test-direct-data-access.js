require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function testDirectDataAccess() {
  console.log('Testing direct data access (bypassing Prisma)...\n')

  try {
    // 1. 测试获取评价数据
    console.log('1. 直接获取评价数据...')
    const { data: ratings, error: ratingsError } = await supabase
      .from('ratings')
      .select(
        `
        id,
        overallRating,
        workLifeBalance,
        compensation,
        culture,
        careerGrowth,
        management,
        title,
        pros,
        cons,
        position,
        isRecommended,
        isAnonymous,
        createdAt
      `
      )
      .order('createdAt', { ascending: false })

    if (ratingsError) {
      console.log('❌ 获取评价数据失败:', ratingsError.message)
      return
    }

    console.log(`✅ 成功获取 ${ratings.length} 条评价数据:`)
    ratings.forEach((rating, index) => {
      console.log(`   ${index + 1}. ${rating.title}`)
      console.log(`      - 综合评分: ${rating.overallRating}`)
      console.log(`      - 工作生活平衡: ${rating.workLifeBalance}`)
      console.log(`      - 薪资待遇: ${rating.compensation}`)
      console.log(`      - 企业文化: ${rating.culture}`)
      console.log(`      - 职业发展: ${rating.careerGrowth}`)
      console.log(`      - 管理水平: ${rating.management}`)
      console.log(`      - 推荐: ${rating.isRecommended ? '是' : '否'}`)
      console.log(`      - 匿名: ${rating.isAnonymous ? '是' : '否'}`)
      console.log('      ---')
    })

    // 2. 测试评价统计
    console.log('\n2. 计算评价统计...')

    if (ratings.length > 0) {
      const stats = {
        totalCount: ratings.length,
        averageOverall: (
          ratings.reduce((sum, r) => sum + r.overallRating, 0) / ratings.length
        ).toFixed(2),
        averageWorkLife: (
          ratings.reduce((sum, r) => sum + (r.workLifeBalance || 0), 0) /
          ratings.length
        ).toFixed(2),
        averageCompensation: (
          ratings.reduce((sum, r) => sum + (r.compensation || 0), 0) /
          ratings.length
        ).toFixed(2),
        averageCulture: (
          ratings.reduce((sum, r) => sum + (r.culture || 0), 0) / ratings.length
        ).toFixed(2),
        averageGrowth: (
          ratings.reduce((sum, r) => sum + (r.careerGrowth || 0), 0) /
          ratings.length
        ).toFixed(2),
        averageManagement: (
          ratings.reduce((sum, r) => sum + (r.management || 0), 0) /
          ratings.length
        ).toFixed(2),
        recommendationRate: Math.round(
          (ratings.filter(r => r.isRecommended).length / ratings.length) * 100
        ),
      }

      console.log('✅ 评价统计结果:')
      console.log(`   - 总评价数: ${stats.totalCount}`)
      console.log(`   - 平均综合评分: ${stats.averageOverall}`)
      console.log(`   - 平均工作生活平衡: ${stats.averageWorkLife}`)
      console.log(`   - 平均薪资待遇: ${stats.averageCompensation}`)
      console.log(`   - 平均企业文化: ${stats.averageCulture}`)
      console.log(`   - 平均职业发展: ${stats.averageGrowth}`)
      console.log(`   - 平均管理水平: ${stats.averageManagement}`)
      console.log(`   - 推荐率: ${stats.recommendationRate}%`)
    }

    // 3. 测试按公司分组
    console.log('\n3. 按公司分组统计...')
    const { data: companies, error: companyError } = await supabase
      .from('companies')
      .select('id, name')
      .limit(5)

    if (!companyError && companies) {
      for (const company of companies) {
        const companyRatings = ratings.filter(r => r.companyId === company.id)
        if (companyRatings.length > 0) {
          const avgRating = (
            companyRatings.reduce((sum, r) => sum + r.overallRating, 0) /
            companyRatings.length
          ).toFixed(2)
          console.log(
            `   - ${company.name}: ${companyRatings.length} 条评价，平均 ${avgRating} 分`
          )
        }
      }
    }

    console.log('\n🎉 数据访问测试完成！字段名和数据结构完全正确。')
  } catch (error) {
    console.error('❌ 测试失败:', error.message)
  }
}

testDirectDataAccess()
