require('dotenv').config({ path: '.env.local' })
const { createClient } = require('@supabase/supabase-js')

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
)

async function createTestRatings() {
  console.log('Creating test ratings data...\n')

  try {
    // 1. 首先获取一个公司ID和用户ID
    console.log('1. 获取公司和用户信息...')

    const { data: companies, error: companyError } = await supabase
      .from('companies')
      .select('id, name')
      .limit(3)

    if (companyError || !companies?.length) {
      console.log('❌ 无法获取公司信息:', companyError?.message)
      return
    }

    const { data: users, error: userError } = await supabase
      .from('users')
      .select('id, email')
      .limit(3)

    if (userError || !users?.length) {
      console.log('❌ 无法获取用户信息:', userError?.message)
      return
    }

    console.log(
      '✅ 找到公司:',
      companies.map(c => c.name)
    )
    console.log(
      '✅ 找到用户:',
      users.map(u => u.email)
    )

    // 2. 创建测试评价数据
    console.log('\n2. 创建测试评价数据...')

    const testRatings = [
      {
        authorId: users[0].id,
        companyId: companies[0].id,
        overallRating: 4.5,
        workLifeBalance: 4,
        compensation: 4,
        culture: 5,
        careerGrowth: 4,
        management: 3,
        title: '优秀的技术公司',
        pros: '技术氛围浓厚，同事专业素质高，学习机会多',
        cons: '工作强度较大，加班相对较多',
        advice: '适合有技术追求的工程师发展',
        isRecommended: true,
        recommendationReason: '技术成长空间大',
        position: '前端工程师',
        department: '技术部',
        workDuration: 2,
        employmentType: 'FULL_TIME',
        isAnonymous: true,
        isActive: true,
        isVerified: false,
      },
      {
        authorId: users[1].id,
        companyId: companies[0].id,
        overallRating: 3.5,
        workLifeBalance: 2,
        compensation: 5,
        culture: 4,
        careerGrowth: 3,
        management: 3,
        title: '薪资不错但工作强度大',
        pros: '薪资水平行业领先，福利待遇好',
        cons: '996工作制，工作压力很大',
        advice: '适合能承受高强度工作的人',
        isRecommended: false,
        recommendationReason: '工作生活平衡较差',
        position: '后端工程师',
        department: '技术部',
        workDuration: 1,
        employmentType: 'FULL_TIME',
        isAnonymous: true,
        isActive: true,
        isVerified: false,
      },
      {
        authorId: users[2].id,
        companyId: companies[1].id,
        overallRating: 4.2,
        workLifeBalance: 5,
        compensation: 3,
        culture: 4,
        careerGrowth: 4,
        management: 4,
        title: '工作环境轻松，适合长期发展',
        pros: '工作氛围轻松，同事关系融洽，很少加班',
        cons: '薪资相对较低，晋升机会有限',
        advice: '适合追求工作生活平衡的人',
        isRecommended: true,
        recommendationReason: '生活质量高',
        position: '产品经理',
        department: '产品部',
        workDuration: 3,
        employmentType: 'FULL_TIME',
        isAnonymous: false,
        isActive: true,
        isVerified: false,
      },
    ]

    // 3. 批量插入评价数据
    const { data: insertedRatings, error: insertError } = await supabase
      .from('ratings')
      .insert(testRatings)
      .select('id, title, overallRating')

    if (insertError) {
      console.log('❌ 插入评价数据失败:', insertError.message)
      return
    }

    console.log('✅ 成功创建评价数据:')
    insertedRatings.forEach((rating, index) => {
      console.log(
        `   ${index + 1}. ${rating.title} (评分: ${rating.overallRating})`
      )
    })

    // 4. 验证数据
    console.log('\n3. 验证插入的数据...')
    const { data: verifyRatings, error: verifyError } = await supabase
      .from('ratings')
      .select(
        'id, overallRating, workLifeBalance, compensation, culture, careerGrowth, title'
      )
      .in(
        'id',
        insertedRatings.map(r => r.id)
      )

    if (verifyError) {
      console.log('❌ 验证数据失败:', verifyError.message)
      return
    }

    console.log('✅ 数据验证成功，字段名正确:')
    verifyRatings.forEach(rating => {
      console.log(`   - ID: ${rating.id}`)
      console.log(`   - 综合评分: ${rating.overallRating}`)
      console.log(`   - 工作生活平衡: ${rating.workLifeBalance}`)
      console.log(`   - 薪资待遇: ${rating.compensation}`)
      console.log(`   - 企业文化: ${rating.culture}`)
      console.log(`   - 职业发展: ${rating.careerGrowth}`)
      console.log(`   - 标题: ${rating.title}`)
      console.log('   ---')
    })

    console.log('\n🎉 测试数据创建成功！现在可以测试API了。')
  } catch (error) {
    console.error('❌ 创建测试数据失败:', error.message)
  }
}

createTestRatings()
