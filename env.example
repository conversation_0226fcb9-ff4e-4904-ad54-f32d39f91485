# ==========================================
# WorkMates 项目环境变量配置
# ==========================================

# Supabase 数据库配置
DATABASE_URL="postgresql://postgres.zfctpeukxaxftfsmpqgp:[YOUR-PASSWORD]@aws-0-ap-northeast-1.pooler.supabase.com:6543/postgres"
DIRECT_URL="postgresql://postgres.zfctpeukxaxftfsmpqgp:[YOUR-PASSWORD]@aws-0-ap-northeast-1.pooler.supabase.com:6543/postgres"

# Supabase API 配置
NEXT_PUBLIC_SUPABASE_URL="https://zfctpeukxaxftfsmpqgp.supabase.co"
NEXT_PUBLIC_SUPABASE_ANON_KEY="eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.*******************************************************************************************************************************.CuPOCJ-RkxXNQb3A7yeTuS0WLtXQ6tF7gkUUEkO3Dx0"
SUPABASE_SERVICE_ROLE_KEY="[从Supabase Dashboard获取]"

# NextAuth.js 配置
NEXTAUTH_SECRET="workmates-secret-key-2024"
NEXTAUTH_URL="http://localhost:3000"

# OAuth Providers (可选)
GITHUB_CLIENT_ID=""
GITHUB_CLIENT_SECRET=""
GOOGLE_CLIENT_ID=""
GOOGLE_CLIENT_SECRET=""

# Email 配置 (可选)
SMTP_HOST=""
SMTP_PORT=""
SMTP_USER=""
SMTP_PASS=""
SMTP_FROM=""

# 文件上传配置
UPLOAD_DIR="./public/uploads"
MAX_FILE_SIZE="5242880" # 5MB
SUPABASE_STORAGE_BUCKET="experience-files"

# App Configuration
APP_NAME="WorkMates"
APP_URL="http://localhost:3000"

# Admin Configuration
ADMIN_EMAIL="<EMAIL>"

# Rate Limiting
RATE_LIMIT_REQUESTS_PER_MINUTE="60"

# Development
NODE_ENV="development"

# ==========================================
# 生产环境配置示例
# ==========================================
# NEXTAUTH_URL="https://your-domain.com"
# APP_URL="https://your-domain.com"
# NODE_ENV="production" 